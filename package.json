{"name": "twt-strapi", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@_sh/strapi-plugin-ckeditor": "^6.0.1", "@4very/strapi-plugin-schemas-to-ts": "^2.0.2", "@strapi/plugin-cloud": "5.12.7", "@strapi/plugin-color-picker": "^5.13.0", "@strapi/plugin-graphql": "^5.13.0", "@strapi/plugin-users-permissions": "5.12.7", "@strapi/strapi": "5.12.7", "better-sqlite3": "^11.10.0", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "093f7adf25f86ee8b4036d5936a28beb5e7640203a62ed8383fb1f649d79b60a"}}
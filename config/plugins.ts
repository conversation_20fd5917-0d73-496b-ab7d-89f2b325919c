export default ({ env }) => ({
  graphql: {
    config: {
      endpoint: "/graphql",
      shadowCRUD: true,
      playgroundAlways: true,
      depthLimit: 25,
      amountLimit: 250,
      defaultLimit: 50,
      maxLimit: 250,
      apolloServer: {
        tracing: false,
        introspection: true,
        cache: "bounded",
      },
    },
  },
  "schemas-to-ts": {
    enabled: true,
    config: {
      acceptedNodeEnvs: ["development"],
      commonInterfacesFolderName: "schemas-to-ts",
      alwaysAddEnumSuffix: true,
      alwaysAddComponentSuffix: false,
      usePrettierIfAvailable: true,
      logLevel: 2,
      destinationFolder: undefined,
    },
  },
  // upload: {
  //   config: {
  //     provider: "aws-s3",
  //     providerOptions: {
  //       baseUrl: env("AWS_MEDIA_URL"),
  //       rootPath: env("CDN_ROOT_PATH", "public"),
  //       s3Options: {
  //         credentials: {
  //           accessKeyId: env("AWS_ACCESS_KEY_ID"),
  //           secretAccessKey: env("AWS_SECRET_ACCESS_KEY"),
  //         },
  //         region: env("AWS_REGION", "ap-south-1"),
  //         params: {
  //           //  ACL: env("AWS_ACL", "public-read"),
  //           signedUrlExpires: env("AWS_SIGNED_URL_EXPIRES", 15 * 60),
  //           Bucket: env("AWS_BUCKET"),
  //         },
  //       },
  //     },
  //     actionOptions: {
  //       upload: {},
  //       uploadStream: {},
  //       delete: {},
  //     },
  //   },
  // },
});

'use client';

import React from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { getStore, getPersistor } from './index';

// ============================================================================
// STORE PROVIDER COMPONENT
// ============================================================================

interface StoreProviderProps {
  children: React.ReactNode;
}

/**
 * Redux Store Provider with persistence support
 * 
 * This component provides the Redux store to the entire application
 * and handles persistence rehydration for cart and theme data.
 */
export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  // Get store and persistor instances
  const store = getStore();
  const persistor = getPersistor();

  return (
    <Provider store={store}>
      <PersistGate 
        loading={<StoreLoadingFallback />} 
        persistor={persistor}
      >
        {children}
      </PersistGate>
    </Provider>
  );
};

// ============================================================================
// LOADING FALLBACK COMPONENT
// ============================================================================

/**
 * Loading fallback component shown during store rehydration
 */
const StoreLoadingFallback: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-gray-600 text-sm">Loading your preferences...</p>
      </div>
    </div>
  );
};

// ============================================================================
// STORE PROVIDER WITH ERROR BOUNDARY
// ============================================================================

interface StoreProviderWithErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
}

/**
 * Store Provider with Error Boundary for production use
 */
export const StoreProviderWithErrorBoundary: React.FC<StoreProviderWithErrorBoundaryProps> = ({ 
  children, 
  fallback: ErrorFallback = DefaultErrorFallback 
}) => {
  return (
    <StoreErrorBoundary fallback={ErrorFallback}>
      <StoreProvider>
        {children}
      </StoreProvider>
    </StoreErrorBoundary>
  );
};

// ============================================================================
// ERROR BOUNDARY COMPONENT
// ============================================================================

interface StoreErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface StoreErrorBoundaryProps {
  children: React.ReactNode;
  fallback: React.ComponentType<{ error: Error; resetError: () => void }>;
}

class StoreErrorBoundary extends React.Component<StoreErrorBoundaryProps, StoreErrorBoundaryState> {
  constructor(props: StoreErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): StoreErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Store Error Boundary caught an error:', error, errorInfo);
    
    // Log to error reporting service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const ErrorFallback = this.props.fallback;
      return <ErrorFallback error={this.state.error} resetError={this.resetError} />;
    }

    return this.props.children;
  }
}

// ============================================================================
// DEFAULT ERROR FALLBACK COMPONENT
// ============================================================================

interface DefaultErrorFallbackProps {
  error: Error;
  resetError: () => void;
}

const DefaultErrorFallback: React.FC<DefaultErrorFallbackProps> = ({ error, resetError }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-red-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <div className="text-red-500 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Something went wrong
        </h2>
        
        <p className="text-gray-600 mb-4">
          There was an error loading the application. Please try refreshing the page.
        </p>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="text-left mb-4 p-3 bg-gray-100 rounded text-sm">
            <summary className="cursor-pointer font-medium">Error Details</summary>
            <pre className="mt-2 whitespace-pre-wrap text-xs text-red-600">
              {error.message}
              {error.stack}
            </pre>
          </details>
        )}
        
        <div className="space-y-2">
          <button
            onClick={resetError}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700 transition-colors"
          >
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// DEVELOPMENT STORE PROVIDER
// ============================================================================

/**
 * Development Store Provider with additional debugging features
 */
export const DevStoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  const store = getStore();
  const persistor = getPersistor();

  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🏪 Redux Store Provider mounted');
      console.log('📦 Store instance:', store);
      console.log('💾 Persistor instance:', persistor);
      
      // Log initial state
      console.log('🔄 Initial store state:', store.getState());
    }
  }, [store, persistor]);

  return (
    <Provider store={store}>
      <PersistGate 
        loading={<DevStoreLoadingFallback />} 
        persistor={persistor}
        onBeforeLift={() => {
          if (process.env.NODE_ENV === 'development') {
            console.log('🚀 Store rehydration complete');
            console.log('🔄 Rehydrated state:', store.getState());
          }
        }}
      >
        {children}
      </PersistGate>
    </Provider>
  );
};

/**
 * Development loading fallback with more detailed information
 */
const DevStoreLoadingFallback: React.FC = () => {
  const [loadingTime, setLoadingTime] = React.useState(0);

  React.useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      setLoadingTime(Date.now() - startTime);
    }, 100);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600 text-sm mb-2">Rehydrating Redux store...</p>
        <p className="text-gray-400 text-xs">Loading time: {loadingTime}ms</p>
        {loadingTime > 2000 && (
          <p className="text-orange-500 text-xs mt-2">
            ⚠️ Store rehydration is taking longer than expected
          </p>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// EXPORTS
// ============================================================================

export default process.env.NODE_ENV === 'development' ? DevStoreProvider : StoreProvider;

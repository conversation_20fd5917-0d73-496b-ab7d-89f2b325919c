"use client";

import React from "react";
import { Provider } from "react-redux";
import { getStore } from "./index";

// ============================================================================
// STORE PROVIDER COMPONENT
// ============================================================================

interface StoreProviderProps {
  children: React.ReactNode;
}

/**
 * Redux Store Provider for instant loading
 * No persistence - provides immediate access to Redux store
 */
export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  const store = getStore();

  return <Provider store={store}>{children}</Provider>;
};

export default StoreProvider;

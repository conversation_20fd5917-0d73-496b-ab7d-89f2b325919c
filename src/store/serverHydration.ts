import { AppStore } from './index';
import { setStrapiProduct, setMedusaProduct, setActiveVariant } from './slices/productSlice';
import { initializeThemeFromProduct } from './slices/themeSlice';
import { ProductDetailsType } from '@/types/Collections/ProductDetails';
import { ExtendedMedusaProductWithStrapiProduct } from '@/types/Medusa/Product';

// ============================================================================
// SERVER-SIDE REDUX HYDRATION UTILITIES
// ============================================================================

/**
 * Interface for product data to be hydrated into Redux store
 */
export interface ProductHydrationData {
  strapiProduct?: ProductDetailsType | null;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct | null;
}

/**
 * Hydrate Redux store with product data on the server-side
 * This ensures the store is pre-populated before the page renders
 */
export const hydrateProductData = (
  store: AppStore,
  data: ProductHydrationData
): void => {
  const { strapiProduct, medusaProduct } = data;

  // Hydrate Strapi product data
  if (strapiProduct) {
    store.dispatch(setStrapiProduct(strapiProduct));
    
    // Initialize theme from Strapi product data
    if (strapiProduct.primary_color || strapiProduct.bg_color) {
      store.dispatch(initializeThemeFromProduct({
        primaryColor: strapiProduct.primary_color,
        backgroundColor: strapiProduct.bg_color,
      }));
    }
  }

  // Hydrate Medusa product data
  if (medusaProduct) {
    store.dispatch(setMedusaProduct(medusaProduct));
    
    // Set first variant as active if available
    if (medusaProduct.variants?.[0]) {
      store.dispatch(setActiveVariant(medusaProduct.variants[0]));
    }
  }
};

/**
 * Create a pre-hydrated store for server-side rendering
 * This is used in Next.js pages to ensure instant loading
 */
export const createHydratedStore = (
  makeStore: () => AppStore,
  productData: ProductHydrationData
): AppStore => {
  const store = makeStore();
  hydrateProductData(store, productData);
  return store;
};

/**
 * Extract serializable state for client-side hydration
 * This ensures the client receives the same state as the server
 */
export const getSerializableState = (store: AppStore) => {
  const state = store.getState();
  
  // Return only the parts of state that should be hydrated
  return {
    product: state.product,
    theme: state.theme,
    // Don't include cart or UI state in SSR hydration
  };
};

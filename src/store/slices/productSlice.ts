import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ProductState, SetProductDataPayload, SetVariantPayload, SetErrorPayload, AsyncThunkConfig } from '../types';
import { ProductDetailsType } from '@/types/Collections/ProductDetails';
import { ExtendedMedusaProductWithStrapiProduct, ExtendedVariant } from '@/types/Medusa/Product';
import { calculateDiscount } from '@/utils/pricing';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: ProductState = {
  // Core product data
  strapiProduct: null,
  medusaProduct: null,
  
  // Variant management
  activeVariant: null,
  
  // Product metadata and computed values
  combinedImages: [],
  currentPrice: 0,
  originalPrice: 0,
  loyaltyPoints: 0,
  discountPercentage: 0,
  breadcrumbItems: [
    { label: "Home", href: "/" },
    { label: "Products", href: "/products" },
    { label: "Product" },
  ],
  
  // Loading and error states
  isLoading: false,
  isError: false,
  error: null,
};

// ============================================================================
// ASYNC THUNKS
// ============================================================================

/**
 * Async thunk for initializing product data
 */
export const initializeProductData = createAsyncThunk<
  { strapiProduct: ProductDetailsType | null; medusaProduct: ExtendedMedusaProductWithStrapiProduct | null },
  SetProductDataPayload,
  AsyncThunkConfig
>(
  'product/initializeProductData',
  async ({ strapiProduct, medusaProduct }, { rejectWithValue }) => {
    try {
      // Validate that at least one product data source is provided
      if (!strapiProduct && !medusaProduct) {
        throw new Error('At least one product data source must be provided');
      }
      
      return {
        strapiProduct: strapiProduct || null,
        medusaProduct: medusaProduct || null,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize product data';
      return rejectWithValue(errorMessage);
    }
  }
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate combined images from global and variant-specific sources
 */
const generateCombinedImages = (
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null,
  activeVariant: ExtendedVariant | null
): string[] => {
  const globalImages: string[] = [];
  const variantImages: string[] = [];

  // Extract global product images
  if (medusaProduct?.images && Array.isArray(medusaProduct.images)) {
    globalImages.push(
      ...medusaProduct.images.map((img: any) => img.url || img)
    );
  }

  // Extract variant-specific images
  if (activeVariant?.variant_image && Array.isArray(activeVariant.variant_image)) {
    const sortedVariantImages = [...activeVariant.variant_image].sort(
      (a, b) => a.rank - b.rank
    );
    variantImages.push(...sortedVariantImages.map((img) => img.url));
  }

  // Combine images
  const combined = [...globalImages, ...variantImages];

  // Fallback to sample images if no images are available
  if (combined.length === 0) {
    return [
      "/images/products/pdp/image.png",
      "/images/products/badaam/highlight/2.webp",
      "/images/products/badaam/highlight/3.webp",
      "/images/products/badaam/highlight/4.webp",
      "/images/products/badaam/highlight/5.webp",
    ];
  }

  return combined;
};

/**
 * Calculate pricing information
 */
const calculatePricing = (activeVariant: ExtendedVariant | null) => {
  if (!activeVariant) {
    return {
      currentPrice: 0,
      originalPrice: 0,
      loyaltyPoints: 0,
      discountPercentage: 0,
    };
  }

  const currentPrice = 
    activeVariant.extended_product_variants?.discounted_price ||
    activeVariant.calculated_price?.calculated_amount ||
    0;

  const originalPrice = activeVariant.calculated_price?.calculated_amount || 0;
  const loyaltyPoints = Math.floor(currentPrice * 0.02);
  const discountPercentage = calculateDiscount(currentPrice, originalPrice);

  return {
    currentPrice,
    originalPrice,
    loyaltyPoints,
    discountPercentage,
  };
};

/**
 * Generate breadcrumb items
 */
const generateBreadcrumbs = (medusaProduct: ExtendedMedusaProductWithStrapiProduct | null) => [
  { label: "Home", href: "/" },
  { label: "Products", href: "/products" },
  { label: medusaProduct?.title || "Product" },
];

// ============================================================================
// SLICE DEFINITION
// ============================================================================

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    // Product data actions
    setStrapiProduct: (state, action: PayloadAction<ProductDetailsType | null>) => {
      state.strapiProduct = action.payload;
      // Update breadcrumbs when product changes
      state.breadcrumbItems = generateBreadcrumbs(state.medusaProduct);
    },

    setMedusaProduct: (state, action: PayloadAction<ExtendedMedusaProductWithStrapiProduct | null>) => {
      state.medusaProduct = action.payload;
      
      // Set first variant as active if available
      if (action.payload?.variants?.[0]) {
        state.activeVariant = action.payload.variants[0];
        
        // Recalculate computed values
        const pricing = calculatePricing(state.activeVariant);
        Object.assign(state, pricing);
        
        state.combinedImages = generateCombinedImages(state.medusaProduct, state.activeVariant);
      }
      
      // Update breadcrumbs
      state.breadcrumbItems = generateBreadcrumbs(action.payload);
    },

    // Variant management
    setActiveVariant: (state, action: PayloadAction<ExtendedVariant>) => {
      state.activeVariant = action.payload;
      
      // Recalculate computed values when variant changes
      const pricing = calculatePricing(action.payload);
      Object.assign(state, pricing);
      
      state.combinedImages = generateCombinedImages(state.medusaProduct, action.payload);
    },

    // Error management
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isError = !!action.payload;
    },

    clearError: (state) => {
      state.error = null;
      state.isError = false;
    },

    // Loading state management
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Reset actions
    resetProductState: (state) => {
      Object.assign(state, initialState);
    },

    // Computed values recalculation (for manual triggers)
    recalculateComputedValues: (state) => {
      const pricing = calculatePricing(state.activeVariant);
      Object.assign(state, pricing);
      
      state.combinedImages = generateCombinedImages(state.medusaProduct, state.activeVariant);
      state.breadcrumbItems = generateBreadcrumbs(state.medusaProduct);
    },
  },
  extraReducers: (builder) => {
    builder
      // Initialize product data
      .addCase(initializeProductData.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(initializeProductData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.strapiProduct = action.payload.strapiProduct;
        state.medusaProduct = action.payload.medusaProduct;
        
        // Set first variant as active if available
        if (action.payload.medusaProduct?.variants?.[0]) {
          state.activeVariant = action.payload.medusaProduct.variants[0];
        }
        
        // Recalculate all computed values
        const pricing = calculatePricing(state.activeVariant);
        Object.assign(state, pricing);
        
        state.combinedImages = generateCombinedImages(state.medusaProduct, state.activeVariant);
        state.breadcrumbItems = generateBreadcrumbs(state.medusaProduct);
      })
      .addCase(initializeProductData.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload || 'Failed to initialize product data';
      });
  },
});

// ============================================================================
// EXPORTS
// ============================================================================

export const {
  setStrapiProduct,
  setMedusaProduct,
  setActiveVariant,
  setError,
  clearError,
  setLoading,
  resetProductState,
  recalculateComputedValues,
} = productSlice.actions;

export default productSlice.reducer;

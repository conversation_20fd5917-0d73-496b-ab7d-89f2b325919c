import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ProductState, SetProductDataPayload } from '../types';
import { ProductDetailsType } from '@/types/Collections/ProductDetails';
import { ExtendedMedusaProductWithStrapiProduct, ExtendedVariant } from '@/types/Medusa/Product';
import { calculateDiscount } from '@/utils/pricing';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: ProductState = {
  strapiProduct: null,
  medusaProduct: null,
  activeVariant: null,
  combinedImages: [],
  currentPrice: 0,
  originalPrice: 0,
  loyaltyPoints: 0,
  discountPercentage: 0,
  breadcrumbItems: [
    { label: "Home", href: "/" },
    { label: "Products", href: "/products" },
    { label: "Product" },
  ],
  isLoading: false,
  isError: false,
  error: null,
};

// ============================================================================
// ASYNC THUNKS
// ============================================================================

export const initializeProductData = createAsyncThunk(
  'product/initializeProductData',
  async ({ strapiProduct, medusaProduct }: SetProductDataPayload, { rejectWithValue }) => {
    try {
      if (!strapiProduct && !medusaProduct) {
        throw new Error('At least one product data source must be provided');
      }
      
      return {
        strapiProduct: strapiProduct || null,
        medusaProduct: medusaProduct || null,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize product data';
      return rejectWithValue(errorMessage);
    }
  }
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const generateCombinedImages = (
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null,
  activeVariant: ExtendedVariant | null
): string[] => {
  const globalImages: string[] = [];
  const variantImages: string[] = [];

  if (medusaProduct?.images && Array.isArray(medusaProduct.images)) {
    globalImages.push(
      ...medusaProduct.images.map((img: any) => img.url || img)
    );
  }

  if (activeVariant?.variant_image && Array.isArray(activeVariant.variant_image)) {
    const sortedVariantImages = [...activeVariant.variant_image].sort(
      (a, b) => a.rank - b.rank
    );
    variantImages.push(...sortedVariantImages.map((img) => img.url));
  }

  const combined = [...globalImages, ...variantImages];

  if (combined.length === 0) {
    return [
      "/images/products/pdp/image.png",
      "/images/products/badaam/highlight/2.webp",
      "/images/products/badaam/highlight/3.webp",
      "/images/products/badaam/highlight/4.webp",
      "/images/products/badaam/highlight/5.webp",
    ];
  }

  return combined;
};

const calculatePricing = (activeVariant: ExtendedVariant | null) => {
  if (!activeVariant) {
    return {
      currentPrice: 0,
      originalPrice: 0,
      loyaltyPoints: 0,
      discountPercentage: 0,
    };
  }

  const currentPrice = 
    activeVariant.extended_product_variants?.discounted_price ||
    activeVariant.calculated_price?.calculated_amount ||
    0;

  const originalPrice = activeVariant.calculated_price?.calculated_amount || 0;
  const loyaltyPoints = Math.floor(currentPrice * 0.02);
  const discountPercentage = calculateDiscount(currentPrice, originalPrice);

  return {
    currentPrice,
    originalPrice,
    loyaltyPoints,
    discountPercentage,
  };
};

const generateBreadcrumbs = (medusaProduct: ExtendedMedusaProductWithStrapiProduct | null) => [
  { label: "Home", href: "/" },
  { label: "Products", href: "/products" },
  { label: medusaProduct?.title || "Product" },
];

// ============================================================================
// SLICE DEFINITION
// ============================================================================

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    setStrapiProduct: (state, action: PayloadAction<ProductDetailsType | null>) => {
      state.strapiProduct = action.payload;
      state.breadcrumbItems = generateBreadcrumbs(state.medusaProduct);
    },

    setMedusaProduct: (state, action: PayloadAction<ExtendedMedusaProductWithStrapiProduct | null>) => {
      state.medusaProduct = action.payload;
      
      if (action.payload?.variants?.[0]) {
        state.activeVariant = action.payload.variants[0];
        const pricing = calculatePricing(state.activeVariant);
        Object.assign(state, pricing);
        state.combinedImages = generateCombinedImages(state.medusaProduct, state.activeVariant);
      }
      
      state.breadcrumbItems = generateBreadcrumbs(action.payload);
    },

    setActiveVariant: (state, action: PayloadAction<ExtendedVariant>) => {
      state.activeVariant = action.payload;
      const pricing = calculatePricing(action.payload);
      Object.assign(state, pricing);
      state.combinedImages = generateCombinedImages(state.medusaProduct, action.payload);
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isError = !!action.payload;
    },

    clearError: (state) => {
      state.error = null;
      state.isError = false;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    resetProductState: (state) => {
      Object.assign(state, initialState);
    },

    recalculateComputedValues: (state) => {
      const pricing = calculatePricing(state.activeVariant);
      Object.assign(state, pricing);
      state.combinedImages = generateCombinedImages(state.medusaProduct, state.activeVariant);
      state.breadcrumbItems = generateBreadcrumbs(state.medusaProduct);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(initializeProductData.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(initializeProductData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.strapiProduct = action.payload.strapiProduct;
        state.medusaProduct = action.payload.medusaProduct;
        
        if (action.payload.medusaProduct?.variants?.[0]) {
          state.activeVariant = action.payload.medusaProduct.variants[0];
        }
        
        const pricing = calculatePricing(state.activeVariant);
        Object.assign(state, pricing);
        
        state.combinedImages = generateCombinedImages(state.medusaProduct, state.activeVariant);
        state.breadcrumbItems = generateBreadcrumbs(state.medusaProduct);
      })
      .addCase(initializeProductData.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload as string || 'Failed to initialize product data';
      });
  },
});

export const {
  setStrapiProduct,
  setMedusaProduct,
  setActiveVariant,
  setError,
  clearError,
  setLoading,
  resetProductState,
  recalculateComputedValues,
} = productSlice.actions;

export default productSlice.reducer;

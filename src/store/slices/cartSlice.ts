import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { CartState, CartItem, AddToCartPayload } from '../types';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: CartState = {
  items: [],
  quantity: 1,
  isAddingToCart: false,
  isCartOpen: false,
  lastAddedItem: null,
  cartError: null,
};

// ============================================================================
// ASYNC THUNKS
// ============================================================================

export const addToCartAsync = createAsyncThunk(
  'cart/addToCartAsync',
  async (payload: AddToCartPayload & { onAddToCart?: (data: AddToCartPayload) => Promise<void> }, { rejectWithValue }) => {
    try {
      const { product, variant, quantity, totalPrice, onAddToCart } = payload;
      
      const cartItem: CartItem = {
        id: `${product.id}-${variant.id}-${Date.now()}`,
        productId: product.id,
        variantId: variant.id,
        quantity,
        price: totalPrice,
        title: product.title,
        image: product.images?.[0]?.url || variant.variant_image?.[0]?.url,
        variant,
        product,
      };

      if (onAddToCart) {
        await onAddToCart({ product, variant, quantity, totalPrice });
      }

      return cartItem;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add item to cart';
      return rejectWithValue(errorMessage);
    }
  }
);

// ============================================================================
// SLICE DEFINITION
// ============================================================================

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    setQuantity: (state, action: PayloadAction<number>) => {
      if (action.payload >= 1) {
        state.quantity = action.payload;
      }
    },

    incrementQuantity: (state) => {
      state.quantity += 1;
    },

    decrementQuantity: (state) => {
      if (state.quantity > 1) {
        state.quantity -= 1;
      }
    },

    setIsAddingToCart: (state, action: PayloadAction<boolean>) => {
      state.isAddingToCart = action.payload;
    },

    setIsCartOpen: (state, action: PayloadAction<boolean>) => {
      state.isCartOpen = action.payload;
    },

    addToCartLocal: (state, action: PayloadAction<CartItem>) => {
      const existingItemIndex = state.items.findIndex(
        item => item.productId === action.payload.productId && item.variantId === action.payload.variantId
      );

      if (existingItemIndex >= 0) {
        state.items[existingItemIndex].quantity += action.payload.quantity;
        state.items[existingItemIndex].price += action.payload.price;
      } else {
        state.items.push(action.payload);
      }

      state.lastAddedItem = action.payload;
    },

    removeFromCartLocal: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
    },

    clearCart: (state) => {
      state.items = [];
      state.lastAddedItem = null;
      state.cartError = null;
    },

    setCartError: (state, action: PayloadAction<string | null>) => {
      state.cartError = action.payload;
    },

    clearCartError: (state) => {
      state.cartError = null;
    },

    resetCartState: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addToCartAsync.pending, (state) => {
        state.isAddingToCart = true;
        state.cartError = null;
      })
      .addCase(addToCartAsync.fulfilled, (state, action) => {
        state.isAddingToCart = false;
        
        const existingItemIndex = state.items.findIndex(
          item => item.productId === action.payload.productId && item.variantId === action.payload.variantId
        );

        if (existingItemIndex >= 0) {
          state.items[existingItemIndex].quantity += action.payload.quantity;
          state.items[existingItemIndex].price += action.payload.price;
        } else {
          state.items.push(action.payload);
        }

        state.lastAddedItem = action.payload;
      })
      .addCase(addToCartAsync.rejected, (state, action) => {
        state.isAddingToCart = false;
        state.cartError = action.payload as string || 'Failed to add item to cart';
      });
  },
});

export const {
  setQuantity,
  incrementQuantity,
  decrementQuantity,
  setIsAddingToCart,
  setIsCartOpen,
  addToCartLocal,
  removeFromCartLocal,
  clearCart,
  setCartError,
  clearCartError,
  resetCartState,
} = cartSlice.actions;

export default cartSlice.reducer;

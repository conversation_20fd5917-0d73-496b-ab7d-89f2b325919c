import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { CartState, CartItem, AddToCartPayload, AsyncThunkConfig } from '../types';
import { ExtendedMedusaProductWithStrapiProduct, ExtendedVariant } from '@/types/Medusa/Product';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: CartState = {
  // Cart data
  items: [],
  
  // Cart UI state
  quantity: 1,
  isAddingToCart: false,
  isCartOpen: false,
  
  // Cart operations state
  lastAddedItem: null,
  cartError: null,
};

// ============================================================================
// ASYNC THUNKS
// ============================================================================

/**
 * Async thunk for adding item to cart
 */
export const addToCartAsync = createAsyncThunk<
  CartItem,
  AddToCartPayload & { onAddToCart?: (data: AddToCartPayload) => Promise<void> },
  AsyncThunkConfig
>(
  'cart/addToCartAsync',
  async ({ product, variant, quantity, totalPrice, onAddToCart }, { rejectWithValue }) => {
    try {
      // Create cart item
      const cartItem: CartItem = {
        id: `${product.id}-${variant.id}-${Date.now()}`,
        productId: product.id,
        variantId: variant.id,
        quantity,
        price: totalPrice,
        title: product.title,
        image: product.images?.[0]?.url || variant.variant_image?.[0]?.url,
        variant,
        product,
      };

      // Call external add to cart handler if provided
      if (onAddToCart) {
        await onAddToCart({ product, variant, quantity, totalPrice });
      }

      return cartItem;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add item to cart';
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Async thunk for removing item from cart
 */
export const removeFromCartAsync = createAsyncThunk<
  string,
  { itemId: string; onRemoveFromCart?: (itemId: string) => Promise<void> },
  AsyncThunkConfig
>(
  'cart/removeFromCartAsync',
  async ({ itemId, onRemoveFromCart }, { rejectWithValue }) => {
    try {
      // Call external remove from cart handler if provided
      if (onRemoveFromCart) {
        await onRemoveFromCart(itemId);
      }

      return itemId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove item from cart';
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Async thunk for updating cart item quantity
 */
export const updateCartItemQuantityAsync = createAsyncThunk<
  { itemId: string; quantity: number },
  { itemId: string; quantity: number; onUpdateQuantity?: (itemId: string, quantity: number) => Promise<void> },
  AsyncThunkConfig
>(
  'cart/updateCartItemQuantityAsync',
  async ({ itemId, quantity, onUpdateQuantity }, { rejectWithValue }) => {
    try {
      if (quantity <= 0) {
        throw new Error('Quantity must be greater than 0');
      }

      // Call external update quantity handler if provided
      if (onUpdateQuantity) {
        await onUpdateQuantity(itemId, quantity);
      }

      return { itemId, quantity };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update item quantity';
      return rejectWithValue(errorMessage);
    }
  }
);

// ============================================================================
// SLICE DEFINITION
// ============================================================================

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    // Quantity management
    setQuantity: (state, action: PayloadAction<number>) => {
      if (action.payload >= 1) {
        state.quantity = action.payload;
      }
    },

    incrementQuantity: (state) => {
      state.quantity += 1;
    },

    decrementQuantity: (state) => {
      if (state.quantity > 1) {
        state.quantity -= 1;
      }
    },

    // Cart UI state management
    setIsAddingToCart: (state, action: PayloadAction<boolean>) => {
      state.isAddingToCart = action.payload;
    },

    setIsCartOpen: (state, action: PayloadAction<boolean>) => {
      state.isCartOpen = action.payload;
    },

    // Cart operations
    addToCartLocal: (state, action: PayloadAction<CartItem>) => {
      // Check if item already exists in cart
      const existingItemIndex = state.items.findIndex(
        item => item.productId === action.payload.productId && item.variantId === action.payload.variantId
      );

      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        state.items[existingItemIndex].quantity += action.payload.quantity;
        state.items[existingItemIndex].price += action.payload.price;
      } else {
        // Add new item to cart
        state.items.push(action.payload);
      }

      state.lastAddedItem = action.payload;
    },

    removeFromCartLocal: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
    },

    updateCartItemQuantityLocal: (state, action: PayloadAction<{ itemId: string; quantity: number }>) => {
      const item = state.items.find(item => item.id === action.payload.itemId);
      if (item) {
        const oldQuantity = item.quantity;
        item.quantity = action.payload.quantity;
        // Adjust price proportionally
        item.price = (item.price / oldQuantity) * action.payload.quantity;
      }
    },

    clearCart: (state) => {
      state.items = [];
      state.lastAddedItem = null;
      state.cartError = null;
    },

    // Error management
    setCartError: (state, action: PayloadAction<string | null>) => {
      state.cartError = action.payload;
    },

    clearCartError: (state) => {
      state.cartError = null;
    },

    // Reset cart state
    resetCartState: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    builder
      // Add to cart async
      .addCase(addToCartAsync.pending, (state) => {
        state.isAddingToCart = true;
        state.cartError = null;
      })
      .addCase(addToCartAsync.fulfilled, (state, action) => {
        state.isAddingToCart = false;
        
        // Check if item already exists in cart
        const existingItemIndex = state.items.findIndex(
          item => item.productId === action.payload.productId && item.variantId === action.payload.variantId
        );

        if (existingItemIndex >= 0) {
          // Update quantity if item exists
          state.items[existingItemIndex].quantity += action.payload.quantity;
          state.items[existingItemIndex].price += action.payload.price;
        } else {
          // Add new item to cart
          state.items.push(action.payload);
        }

        state.lastAddedItem = action.payload;
      })
      .addCase(addToCartAsync.rejected, (state, action) => {
        state.isAddingToCart = false;
        state.cartError = action.payload || 'Failed to add item to cart';
      })

      // Remove from cart async
      .addCase(removeFromCartAsync.pending, (state) => {
        state.cartError = null;
      })
      .addCase(removeFromCartAsync.fulfilled, (state, action) => {
        state.items = state.items.filter(item => item.id !== action.payload);
      })
      .addCase(removeFromCartAsync.rejected, (state, action) => {
        state.cartError = action.payload || 'Failed to remove item from cart';
      })

      // Update cart item quantity async
      .addCase(updateCartItemQuantityAsync.pending, (state) => {
        state.cartError = null;
      })
      .addCase(updateCartItemQuantityAsync.fulfilled, (state, action) => {
        const item = state.items.find(item => item.id === action.payload.itemId);
        if (item) {
          const oldQuantity = item.quantity;
          item.quantity = action.payload.quantity;
          // Adjust price proportionally
          item.price = (item.price / oldQuantity) * action.payload.quantity;
        }
      })
      .addCase(updateCartItemQuantityAsync.rejected, (state, action) => {
        state.cartError = action.payload || 'Failed to update item quantity';
      });
  },
});

// ============================================================================
// EXPORTS
// ============================================================================

export const {
  setQuantity,
  incrementQuantity,
  decrementQuantity,
  setIsAddingToCart,
  setIsCartOpen,
  addToCartLocal,
  removeFromCartLocal,
  updateCartItemQuantityLocal,
  clearCart,
  setCartError,
  clearCartError,
  resetCartState,
} = cartSlice.actions;

export default cartSlice.reducer;

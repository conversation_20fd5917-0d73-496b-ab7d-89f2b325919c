import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ThemeState, SetThemePayload } from '../types';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: ThemeState = {
  primaryColor: "#036A38",
  backgroundColor: "#ffffff",
  cssVariables: {
    "--product-primary-color": "#036A38",
    "--product-background-color": "#ffffff",
  },
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const generateCSSVariables = (primaryColor: string, backgroundColor: string): Record<string, string> => ({
  "--product-primary-color": primaryColor,
  "--product-background-color": backgroundColor,
  "--product-primary-color-rgb": hexToRgb(primaryColor),
  "--product-background-color-rgb": hexToRgb(backgroundColor),
  "--product-primary-color-alpha-10": `${primaryColor}1a`,
  "--product-primary-color-alpha-20": `${primaryColor}33`,
  "--product-primary-color-alpha-30": `${primaryColor}4d`,
  "--product-primary-color-alpha-50": `${primaryColor}80`,
  "--product-primary-color-alpha-70": `${primaryColor}b3`,
  "--product-primary-color-alpha-90": `${primaryColor}e6`,
});

const hexToRgb = (hex: string): string => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return "0, 0, 0";
  
  return [
    parseInt(result[1], 16),
    parseInt(result[2], 16),
    parseInt(result[3], 16)
  ].join(", ");
};

const isValidHexColor = (color: string): boolean => {
  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
};

// ============================================================================
// SLICE DEFINITION
// ============================================================================

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setPrimaryColor: (state, action: PayloadAction<string>) => {
      if (isValidHexColor(action.payload)) {
        state.primaryColor = action.payload;
        state.cssVariables = generateCSSVariables(action.payload, state.backgroundColor);
      }
    },

    setBackgroundColor: (state, action: PayloadAction<string>) => {
      if (isValidHexColor(action.payload)) {
        state.backgroundColor = action.payload;
        state.cssVariables = generateCSSVariables(state.primaryColor, action.payload);
      }
    },

    setThemeColors: (state, action: PayloadAction<SetThemePayload>) => {
      const { primaryColor, backgroundColor } = action.payload;
      
      if (primaryColor && isValidHexColor(primaryColor)) {
        state.primaryColor = primaryColor;
      }
      
      if (backgroundColor && isValidHexColor(backgroundColor)) {
        state.backgroundColor = backgroundColor;
      }
      
      state.cssVariables = generateCSSVariables(state.primaryColor, state.backgroundColor);
    },

    initializeThemeFromProduct: (state, action: PayloadAction<{ primaryColor?: string; backgroundColor?: string }>) => {
      const { primaryColor, backgroundColor } = action.payload;
      
      const newPrimaryColor = (primaryColor && isValidHexColor(primaryColor)) 
        ? primaryColor 
        : state.primaryColor;
        
      const newBackgroundColor = (backgroundColor && isValidHexColor(backgroundColor)) 
        ? backgroundColor 
        : state.backgroundColor;
      
      state.primaryColor = newPrimaryColor;
      state.backgroundColor = newBackgroundColor;
      state.cssVariables = generateCSSVariables(newPrimaryColor, newBackgroundColor);
    },

    resetTheme: (state) => {
      Object.assign(state, initialState);
    },

    applyPredefinedTheme: (state, action: PayloadAction<'default' | 'dark' | 'light' | 'green' | 'blue'>) => {
      const themes = {
        default: {
          primaryColor: "#036A38",
          backgroundColor: "#ffffff",
        },
        dark: {
          primaryColor: "#4ade80",
          backgroundColor: "#1f2937",
        },
        light: {
          primaryColor: "#059669",
          backgroundColor: "#f9fafb",
        },
        green: {
          primaryColor: "#16a34a",
          backgroundColor: "#f0fdf4",
        },
        blue: {
          primaryColor: "#2563eb",
          backgroundColor: "#eff6ff",
        },
      };

      const selectedTheme = themes[action.payload];
      state.primaryColor = selectedTheme.primaryColor;
      state.backgroundColor = selectedTheme.backgroundColor;
      state.cssVariables = generateCSSVariables(selectedTheme.primaryColor, selectedTheme.backgroundColor);
    },

    updateCSSVariables: (state, action: PayloadAction<Record<string, string>>) => {
      state.cssVariables = {
        ...state.cssVariables,
        ...action.payload,
      };
    },

    generateThemeVariations: (state) => {
      state.cssVariables = {
        ...state.cssVariables,
        ...generateCSSVariables(state.primaryColor, state.backgroundColor),
      };
    },
  },
});

export const {
  setPrimaryColor,
  setBackgroundColor,
  setThemeColors,
  initializeThemeFromProduct,
  resetTheme,
  applyPredefinedTheme,
  updateCSSVariables,
  generateThemeVariations,
} = themeSlice.actions;

export default themeSlice.reducer;

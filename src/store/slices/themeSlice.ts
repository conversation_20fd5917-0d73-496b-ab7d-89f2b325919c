import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ThemeState, SetThemePayload } from '../types';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: ThemeState = {
  // Color theming
  primaryColor: "#036A38",
  backgroundColor: "#ffffff",
  
  // CSS custom properties
  cssVariables: {
    "--product-primary-color": "#036A38",
    "--product-background-color": "#ffffff",
  },
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate CSS custom properties from theme colors
 */
const generateCSSVariables = (primaryColor: string, backgroundColor: string): Record<string, string> => ({
  "--product-primary-color": primaryColor,
  "--product-background-color": backgroundColor,
  "--product-primary-color-rgb": hexToRgb(primaryColor),
  "--product-background-color-rgb": hexToRgb(backgroundColor),
  "--product-primary-color-alpha-10": `${primaryColor}1a`,
  "--product-primary-color-alpha-20": `${primaryColor}33`,
  "--product-primary-color-alpha-30": `${primaryColor}4d`,
  "--product-primary-color-alpha-50": `${primaryColor}80`,
  "--product-primary-color-alpha-70": `${primaryColor}b3`,
  "--product-primary-color-alpha-90": `${primaryColor}e6`,
});

/**
 * Convert hex color to RGB values
 */
const hexToRgb = (hex: string): string => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return "0, 0, 0";
  
  return [
    parseInt(result[1], 16),
    parseInt(result[2], 16),
    parseInt(result[3], 16)
  ].join(", ");
};

/**
 * Validate hex color format
 */
const isValidHexColor = (color: string): boolean => {
  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
};

/**
 * Get contrasting text color for a given background color
 */
const getContrastingTextColor = (backgroundColor: string): string => {
  // Remove # if present
  const hex = backgroundColor.replace('#', '');
  
  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Return black for light backgrounds, white for dark backgrounds
  return luminance > 0.5 ? '#000000' : '#ffffff';
};

// ============================================================================
// SLICE DEFINITION
// ============================================================================

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    // Primary color management
    setPrimaryColor: (state, action: PayloadAction<string>) => {
      if (isValidHexColor(action.payload)) {
        state.primaryColor = action.payload;
        state.cssVariables = generateCSSVariables(action.payload, state.backgroundColor);
      }
    },

    // Background color management
    setBackgroundColor: (state, action: PayloadAction<string>) => {
      if (isValidHexColor(action.payload)) {
        state.backgroundColor = action.payload;
        state.cssVariables = generateCSSVariables(state.primaryColor, action.payload);
      }
    },

    // Set both colors at once
    setThemeColors: (state, action: PayloadAction<SetThemePayload>) => {
      const { primaryColor, backgroundColor } = action.payload;
      
      if (primaryColor && isValidHexColor(primaryColor)) {
        state.primaryColor = primaryColor;
      }
      
      if (backgroundColor && isValidHexColor(backgroundColor)) {
        state.backgroundColor = backgroundColor;
      }
      
      state.cssVariables = generateCSSVariables(state.primaryColor, state.backgroundColor);
    },

    // Initialize theme from product data
    initializeThemeFromProduct: (state, action: PayloadAction<{ primaryColor?: string; backgroundColor?: string }>) => {
      const { primaryColor, backgroundColor } = action.payload;
      
      // Use provided colors or keep current ones
      const newPrimaryColor = (primaryColor && isValidHexColor(primaryColor)) 
        ? primaryColor 
        : state.primaryColor;
        
      const newBackgroundColor = (backgroundColor && isValidHexColor(backgroundColor)) 
        ? backgroundColor 
        : state.backgroundColor;
      
      state.primaryColor = newPrimaryColor;
      state.backgroundColor = newBackgroundColor;
      state.cssVariables = generateCSSVariables(newPrimaryColor, newBackgroundColor);
    },

    // Reset theme to default
    resetTheme: (state) => {
      Object.assign(state, initialState);
    },

    // Apply predefined theme
    applyPredefinedTheme: (state, action: PayloadAction<'default' | 'dark' | 'light' | 'green' | 'blue'>) => {
      const themes = {
        default: {
          primaryColor: "#036A38",
          backgroundColor: "#ffffff",
        },
        dark: {
          primaryColor: "#4ade80",
          backgroundColor: "#1f2937",
        },
        light: {
          primaryColor: "#059669",
          backgroundColor: "#f9fafb",
        },
        green: {
          primaryColor: "#16a34a",
          backgroundColor: "#f0fdf4",
        },
        blue: {
          primaryColor: "#2563eb",
          backgroundColor: "#eff6ff",
        },
      };

      const selectedTheme = themes[action.payload];
      state.primaryColor = selectedTheme.primaryColor;
      state.backgroundColor = selectedTheme.backgroundColor;
      state.cssVariables = generateCSSVariables(selectedTheme.primaryColor, selectedTheme.backgroundColor);
    },

    // Update CSS variables manually (for advanced customization)
    updateCSSVariables: (state, action: PayloadAction<Record<string, string>>) => {
      state.cssVariables = {
        ...state.cssVariables,
        ...action.payload,
      };
    },

    // Generate theme variations
    generateThemeVariations: (state) => {
      state.cssVariables = {
        ...state.cssVariables,
        ...generateCSSVariables(state.primaryColor, state.backgroundColor),
        "--product-text-color": getContrastingTextColor(state.backgroundColor),
        "--product-text-color-inverse": getContrastingTextColor(state.primaryColor),
      };
    },
  },
});

// ============================================================================
// EXPORTS
// ============================================================================

export const {
  setPrimaryColor,
  setBackgroundColor,
  setThemeColors,
  initializeThemeFromProduct,
  resetTheme,
  applyPredefinedTheme,
  updateCSSVariables,
  generateThemeVariations,
} = themeSlice.actions;

export default themeSlice.reducer;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UIState } from '../types';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: UIState = {
  isModalOpen: false,
  selectedImageIndex: 0,
  currentSlide: 0,
  isUILoading: false,
  uiError: null,
};

// ============================================================================
// SLICE DEFINITION
// ============================================================================

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Modal management
    openModal: (state, action: PayloadAction<number | undefined>) => {
      state.isModalOpen = true;
      if (action.payload !== undefined) {
        state.selectedImageIndex = action.payload;
      }
    },

    closeModal: (state) => {
      state.isModalOpen = false;
    },

    setSelectedImageIndex: (state, action: PayloadAction<number>) => {
      if (action.payload >= 0) {
        state.selectedImageIndex = action.payload;
      }
    },

    // Carousel management
    setCurrentSlide: (state, action: PayloadAction<number>) => {
      if (action.payload >= 0) {
        state.currentSlide = action.payload;
      }
    },

    nextSlide: (state, action: PayloadAction<number>) => {
      const totalSlides = action.payload;
      if (totalSlides > 0) {
        state.currentSlide = (state.currentSlide + 1) % totalSlides;
      }
    },

    previousSlide: (state, action: PayloadAction<number>) => {
      const totalSlides = action.payload;
      if (totalSlides > 0) {
        state.currentSlide = state.currentSlide === 0 
          ? totalSlides - 1 
          : state.currentSlide - 1;
      }
    },

    goToSlide: (state, action: PayloadAction<{ slideIndex: number; totalSlides: number }>) => {
      const { slideIndex, totalSlides } = action.payload;
      if (slideIndex >= 0 && slideIndex < totalSlides) {
        state.currentSlide = slideIndex;
      }
    },

    // Modal image navigation
    nextModalImage: (state, action: PayloadAction<number>) => {
      const totalImages = action.payload;
      if (totalImages > 0) {
        state.selectedImageIndex = (state.selectedImageIndex + 1) % totalImages;
      }
    },

    previousModalImage: (state, action: PayloadAction<number>) => {
      const totalImages = action.payload;
      if (totalImages > 0) {
        state.selectedImageIndex = state.selectedImageIndex === 0 
          ? totalImages - 1 
          : state.selectedImageIndex - 1;
      }
    },

    // Combined actions
    openModalAtSlide: (state, action: PayloadAction<number>) => {
      state.isModalOpen = true;
      state.selectedImageIndex = action.payload;
    },

    closeModalAndSyncCarousel: (state) => {
      state.isModalOpen = false;
      state.currentSlide = state.selectedImageIndex;
    },

    syncModalWithCarousel: (state) => {
      state.selectedImageIndex = state.currentSlide;
    },

    syncCarouselWithModal: (state) => {
      state.currentSlide = state.selectedImageIndex;
    },

    // Loading state management
    setUILoading: (state, action: PayloadAction<boolean>) => {
      state.isUILoading = action.payload;
    },

    // Error management
    setUIError: (state, action: PayloadAction<string | null>) => {
      state.uiError = action.payload;
    },

    clearUIError: (state) => {
      state.uiError = null;
    },

    // Reset actions
    resetUIState: (state) => {
      Object.assign(state, initialState);
    },

    resetModalState: (state) => {
      state.isModalOpen = false;
      state.selectedImageIndex = 0;
    },

    resetCarouselState: (state) => {
      state.currentSlide = 0;
    },

    // Keyboard navigation support
    handleKeyboardNavigation: (state, action: PayloadAction<{ key: string; totalItems: number; isModal: boolean }>) => {
      const { key, totalItems, isModal } = action.payload;
      
      if (totalItems <= 0) return;
      
      const currentIndex = isModal ? state.selectedImageIndex : state.currentSlide;
      
      switch (key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          const prevIndex = currentIndex === 0 ? totalItems - 1 : currentIndex - 1;
          if (isModal) {
            state.selectedImageIndex = prevIndex;
          } else {
            state.currentSlide = prevIndex;
          }
          break;
          
        case 'ArrowRight':
        case 'ArrowDown':
          const nextIndex = (currentIndex + 1) % totalItems;
          if (isModal) {
            state.selectedImageIndex = nextIndex;
          } else {
            state.currentSlide = nextIndex;
          }
          break;
          
        case 'Home':
          if (isModal) {
            state.selectedImageIndex = 0;
          } else {
            state.currentSlide = 0;
          }
          break;
          
        case 'End':
          if (isModal) {
            state.selectedImageIndex = totalItems - 1;
          } else {
            state.currentSlide = totalItems - 1;
          }
          break;
          
        case 'Escape':
          if (isModal) {
            state.isModalOpen = false;
          }
          break;
      }
    },
  },
});

export const {
  openModal,
  closeModal,
  setSelectedImageIndex,
  setCurrentSlide,
  nextSlide,
  previousSlide,
  goToSlide,
  nextModalImage,
  previousModalImage,
  openModalAtSlide,
  closeModalAndSyncCarousel,
  syncModalWithCarousel,
  syncCarouselWithModal,
  setUILoading,
  setUIError,
  clearUIError,
  resetUIState,
  resetModalState,
  resetCarouselState,
  handleKeyboardNavigation,
} = uiSlice.actions;

export default uiSlice.reducer;

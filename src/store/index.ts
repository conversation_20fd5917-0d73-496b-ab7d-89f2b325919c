"use client";

import { configureStore, combineReducers } from "@reduxjs/toolkit";
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import storage from "redux-persist/lib/storage";
import { createWrapper } from "next-redux-wrapper";

// Import reducers
import productReducer from "./slices/productSlice";
import cartReducer from "./slices/cartSlice";
import themeReducer from "./slices/themeSlice";
import uiReducer from "./slices/uiSlice";

// ============================================================================
// PERSISTENCE CONFIGURATION
// ============================================================================

/**
 * Cart persistence configuration
 * Only persist cart items and quantity, not loading states
 */
const cartPersistConfig = {
  key: "cart",
  storage,
  whitelist: ["items", "quantity"], // Only persist these fields
  blacklist: ["isAddingToCart", "cartError", "lastAddedItem"], // Don't persist these
};

/**
 * Theme persistence configuration
 * Persist theme colors for user preference
 */
const themePersistConfig = {
  key: "theme",
  storage,
  whitelist: ["primaryColor", "backgroundColor"], // Only persist color preferences
  blacklist: ["cssVariables"], // Don't persist computed values
};

/**
 * Root persistence configuration
 */
const rootPersistConfig = {
  key: "root",
  storage,
  whitelist: ["cart", "theme"], // Only persist cart and theme
  blacklist: ["product", "ui"], // Don't persist product data and UI state
};

// ============================================================================
// REDUCER CONFIGURATION
// ============================================================================

/**
 * Root reducer combining all slice reducers
 */
const rootReducer = combineReducers({
  product: productReducer,
  cart: persistReducer(cartPersistConfig, cartReducer),
  theme: persistReducer(themePersistConfig, themeReducer),
  ui: uiReducer,
});

/**
 * Persisted root reducer
 */
const persistedReducer = persistReducer(rootPersistConfig, rootReducer);

// ============================================================================
// STORE CONFIGURATION
// ============================================================================

/**
 * Create the Redux store with proper middleware configuration
 */
export const makeStore = () => {
  const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          // Ignore redux-persist actions
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
          // Ignore non-serializable values in specific paths
          ignoredPaths: ["register"],
        },
        // Enable additional checks in development
        immutableCheck: process.env.NODE_ENV === "development",
        actionCreatorCheck: process.env.NODE_ENV === "development",
      }),
    devTools: process.env.NODE_ENV === "development" && {
      // Enhanced Redux DevTools configuration
      name: "TWT PDP Store",
      trace: true,
      traceLimit: 25,
      actionSanitizer: (action) => ({
        ...action,
        // Sanitize large payloads in development
        payload:
          action.type.includes("setMedusaProduct") && action.payload
            ? { ...action.payload, images: "[IMAGES_ARRAY]" }
            : action.payload,
      }),
      stateSanitizer: (state) => ({
        ...state,
        // Sanitize large state objects for better DevTools performance
        product: {
          ...state.product,
          combinedImages:
            state.product.combinedImages.length > 0
              ? `[${state.product.combinedImages.length} images]`
              : [],
        },
      }),
    },
    // Enable enhanced Redux DevTools features
    enhancers: (getDefaultEnhancers) =>
      getDefaultEnhancers({
        autoBatch: true,
        immutableCheck: process.env.NODE_ENV === "development",
        serializableCheck: process.env.NODE_ENV === "development",
      }),
  });

  return store;
};

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];

// ============================================================================
// NEXT.JS WRAPPER CONFIGURATION
// ============================================================================

/**
 * Next.js Redux wrapper for SSR/SSG support
 */
export const wrapper = createWrapper<AppStore>(makeStore, {
  debug: process.env.NODE_ENV === "development",
  serializeState: (state) => JSON.stringify(state),
  deserializeState: (state) => JSON.parse(state),
});

// ============================================================================
// STORE INSTANCE FOR CLIENT-SIDE
// ============================================================================

let store: AppStore;
let persistor: any;

/**
 * Get or create store instance for client-side usage
 */
export const getStore = () => {
  if (typeof window === "undefined") {
    // Server-side: always create a new store
    return makeStore();
  }

  if (!store) {
    // Client-side: create store once
    store = makeStore();
    persistor = persistStore(store);
  }

  return store;
};

/**
 * Get persistor instance
 */
export const getPersistor = () => {
  if (!persistor && typeof window !== "undefined") {
    store = getStore();
    persistor = persistStore(store);
  }
  return persistor;
};

// ============================================================================
// STORE UTILITIES
// ============================================================================

/**
 * Reset store to initial state
 */
export const resetStore = () => {
  if (typeof window !== "undefined" && store) {
    store.dispatch({ type: "RESET_STORE" });
  }
};

/**
 * Clear persisted state
 */
export const clearPersistedState = async () => {
  if (typeof window !== "undefined" && persistor) {
    await persistor.purge();
  }
};

/**
 * Get current store state (for debugging)
 */
export const getStoreState = (): RootState | null => {
  if (typeof window !== "undefined" && store) {
    return store.getState();
  }
  return null;
};

/**
 * Subscribe to store changes (for debugging)
 */
export const subscribeToStore = (callback: (state: RootState) => void) => {
  if (typeof window !== "undefined" && store) {
    return store.subscribe(() => {
      callback(store.getState());
    });
  }
  return () => {};
};

// ============================================================================
// DEVELOPMENT UTILITIES
// ============================================================================

if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
  // Expose store to window for debugging
  (window as any).__REDUX_STORE__ = getStore;
  (window as any).__REDUX_PERSISTOR__ = getPersistor;

  // Log store initialization
  console.log("🏪 Redux store initialized with persistence");

  // Subscribe to state changes for debugging
  subscribeToStore((state) => {
    console.log("🔄 Store state updated:", {
      product: {
        hasData: !!(state.product.strapiProduct || state.product.medusaProduct),
        activeVariant: !!state.product.activeVariant,
        isLoading: state.product.isLoading,
        error: state.product.error,
      },
      cart: {
        itemCount: state.cart.items.length,
        totalItems: state.cart.items.reduce(
          (sum, item) => sum + item.quantity,
          0
        ),
        isAddingToCart: state.cart.isAddingToCart,
      },
      theme: {
        primaryColor: state.theme.primaryColor,
        backgroundColor: state.theme.backgroundColor,
      },
      ui: {
        isModalOpen: state.ui.isModalOpen,
        currentSlide: state.ui.currentSlide,
        selectedImageIndex: state.ui.selectedImageIndex,
      },
    });
  });
}

// ============================================================================
// EXPORTS
// ============================================================================

export default getStore;

// Re-export everything from hooks and other modules
export * from "./hooks";
export * from "./slices/productSlice";
export * from "./slices/cartSlice";
export * from "./slices/themeSlice";
export * from "./slices/uiSlice";
export * from "./selectors/productSelectors";
export * from "./selectors/cartSelectors";
export * from "./selectors/themeSelectors";
export * from "./selectors/uiSelectors";
export { StoreProvider } from "./StoreProvider";

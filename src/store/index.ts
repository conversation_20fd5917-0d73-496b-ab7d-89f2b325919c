"use client";

import { configureStore, combineReducers } from "@reduxjs/toolkit";
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import storage from "redux-persist/lib/storage";

// Import reducers
import productReducer from "./slices/productSlice";
import cartReducer from "./slices/cartSlice";
import themeReducer from "./slices/themeSlice";
import uiReducer from "./slices/uiSlice";

// ============================================================================
// PERSISTENCE CONFIGURATION
// ============================================================================

/**
 * Cart persistence configuration
 */
const cartPersistConfig = {
  key: "cart",
  storage,
  whitelist: ["items", "quantity"],
  blacklist: ["isAddingToCart", "cartError", "lastAddedItem"],
};

/**
 * Theme persistence configuration
 */
const themePersistConfig = {
  key: "theme",
  storage,
  whitelist: ["primaryColor", "backgroundColor"],
  blacklist: ["cssVariables"],
};

/**
 * Root persistence configuration
 */
const rootPersistConfig = {
  key: "root",
  storage,
  whitelist: ["cart", "theme"],
  blacklist: ["product", "ui"],
  // Add version to force migration and clear old state
  version: 1,
  migrate: (state: any) => {
    // Clear any old state that doesn't match our current structure
    if (state && typeof state === "object") {
      const { cart, theme } = state;
      return {
        cart: cart || undefined,
        theme: theme || undefined,
      };
    }
    return undefined;
  },
};

// ============================================================================
// REDUCER CONFIGURATION
// ============================================================================

/**
 * Root reducer combining all slice reducers
 */
const rootReducer = combineReducers({
  product: productReducer,
  cart: persistReducer(cartPersistConfig, cartReducer),
  theme: persistReducer(themePersistConfig, themeReducer),
  ui: uiReducer,
});

/**
 * Persisted root reducer
 */
const persistedReducer = persistReducer(rootPersistConfig, rootReducer);

// ============================================================================
// STORE CONFIGURATION
// ============================================================================

/**
 * Create the Redux store with proper middleware configuration
 */
export const makeStore = () => {
  const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
          ignoredPaths: ["register"],
        },
        immutableCheck: process.env.NODE_ENV === "development",
        actionCreatorCheck: process.env.NODE_ENV === "development",
      }),
    devTools: process.env.NODE_ENV === "development" && {
      name: "TWT PDP Store",
      trace: true,
      traceLimit: 25,
    },
  });

  return store;
};

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];

// ============================================================================
// STORE INSTANCE FOR CLIENT-SIDE
// ============================================================================

let store: AppStore;
let persistor: any;

/**
 * Get or create store instance for client-side usage
 */
export const getStore = () => {
  if (typeof window === "undefined") {
    return makeStore();
  }

  if (!store) {
    store = makeStore();
    persistor = persistStore(store);
  }

  return store;
};

/**
 * Get persistor instance
 */
export const getPersistor = () => {
  if (!persistor && typeof window !== "undefined") {
    store = getStore();
    persistor = persistStore(store);
  }
  return persistor;
};

// ============================================================================
// EXPORTS
// ============================================================================

export default getStore;

// Re-export everything from hooks and other modules
export * from "./hooks";
export * from "./slices/productSlice";
export * from "./slices/cartSlice";
export * from "./slices/themeSlice";
export * from "./slices/uiSlice";
export { StoreProvider } from "./StoreProvider";

"use client";

import { configureStore, combineReducers } from "@reduxjs/toolkit";
import { createWrapper } from "next-redux-wrapper";

// Import reducers
import productReducer from "./slices/productSlice";
import cartReducer from "./slices/cartSlice";
import themeReducer from "./slices/themeSlice";
import uiReducer from "./slices/uiSlice";

// ============================================================================
// REDUCER CONFIGURATION
// ============================================================================

/**
 * Root reducer combining all slice reducers
 * No persistence - for instant loading and better performance
 */
const rootReducer = combineReducers({
  product: productReducer,
  cart: cartReducer,
  theme: themeReducer,
  ui: uiReducer,
});

// ============================================================================
// STORE CONFIGURATION
// ============================================================================

/**
 * Create the Redux store with optimized configuration for instant loading
 */
export const makeStore = () => {
  const store = configureStore({
    reducer: rootReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        // Enable all checks in development for better debugging
        immutableCheck: process.env.NODE_ENV === "development",
        actionCreatorCheck: process.env.NODE_ENV === "development",
        serializableCheck: process.env.NODE_ENV === "development",
      }),
    devTools: process.env.NODE_ENV === "development" && {
      name: "TWT Frontend Store",
      trace: true,
      traceLimit: 25,
    },
  });

  return store;
};

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];

// ============================================================================
// NEXT.JS WRAPPER FOR SSR/SSG
// ============================================================================

/**
 * Next.js Redux wrapper for SSR/SSG support
 * Enables server-side rendering with Redux state
 */
export const wrapper = createWrapper<AppStore>(makeStore, {
  debug: process.env.NODE_ENV === "development",
  serializeState: (state) => JSON.stringify(state),
  deserializeState: (state) => JSON.parse(state),
});

// ============================================================================
// STORE INSTANCE FOR CLIENT-SIDE
// ============================================================================

let store: AppStore;

/**
 * Get or create store instance for client-side usage
 * No persistence - creates fresh store each time for instant loading
 */
export const getStore = () => {
  if (typeof window === "undefined") {
    // Server-side: always create a new store
    return makeStore();
  }

  if (!store) {
    // Client-side: create store once
    store = makeStore();
  }

  return store;
};

// ============================================================================
// EXPORTS
// ============================================================================

export default getStore;

// Re-export everything from hooks and other modules
export * from "./hooks";
export * from "./slices/productSlice";
export * from "./slices/cartSlice";
export * from "./slices/themeSlice";
export * from "./slices/uiSlice";
export { StoreProvider } from "./StoreProvider";

import { useDispatch, useSelector } from "react-redux";
import type { RootState, AppDispatch } from "./index";

// Re-export all hooks from the hooks directory
export * from "./hooks";

// ============================================================================
// TYPED REDUX HOOKS
// ============================================================================

/**
 * Typed version of useDispatch hook
 */
export const useAppDispatch = () => useDispatch<AppDispatch>();

/**
 * Typed version of useSelector hook
 */
export const useAppSelector = <TSelected>(
  selector: (state: RootState) => TSelected
) => useSelector(selector);

// ============================================================================
// CONVENIENCE HOOKS FOR COMMON PATTERNS
// ============================================================================

/**
 * Hook for dispatching actions with loading state management
 */
export const useAppDispatchWithLoading = () => {
  const dispatch = useAppDispatch();

  return async <T>(action: any): Promise<T> => {
    try {
      const result = await dispatch(action);
      return result.payload as T;
    } catch (error) {
      throw error;
    }
  };
};

/**
 * Hook for selecting state with memoization
 */
export const useAppSelectorMemo = <T>(
  selector: (state: RootState) => T,
  equalityFn?: (left: T, right: T) => boolean
) => {
  return useSelector(selector, equalityFn);
};

// ============================================================================
// DOMAIN-SPECIFIC HOOKS
// ============================================================================

/**
 * Hook for product-related state and actions
 */
export const useProductStore = () => {
  const dispatch = useAppDispatch();
  const productState = useAppSelector((state) => state.product);

  return {
    ...productState,
    dispatch,
  };
};

/**
 * Hook for cart-related state and actions
 */
export const useCartStore = () => {
  const dispatch = useAppDispatch();
  const cartState = useAppSelector((state) => state.cart);

  return {
    ...cartState,
    dispatch,
  };
};

/**
 * Hook for theme-related state and actions
 */
export const useThemeStore = () => {
  const dispatch = useAppDispatch();
  const themeState = useAppSelector((state) => state.theme);

  return {
    ...themeState,
    dispatch,
  };
};

/**
 * Hook for UI-related state and actions
 */
export const useUIStore = () => {
  const dispatch = useAppDispatch();
  const uiState = useAppSelector((state) => state.ui);

  return {
    ...uiState,
    dispatch,
  };
};

// ============================================================================
// COMPOSITE HOOKS FOR COMPLEX OPERATIONS
// ============================================================================

/**
 * Hook for complete PDP state management
 */
export const usePDPStore = () => {
  const dispatch = useAppDispatch();
  const state = useAppSelector((state) => state);

  return {
    product: state.product,
    cart: state.cart,
    theme: state.theme,
    ui: state.ui,
    dispatch,
  };
};

/**
 * Hook for state synchronization between slices
 */
export const useStateSynchronization = () => {
  // const dispatch = useAppDispatch(); // Will be used when actions are implemented

  return {
    syncModalWithCarousel: () => {
      // Implementation will be added when we create the actions
    },
    syncCarouselWithModal: () => {
      // Implementation will be added when we create the actions
    },
    syncThemeWithProduct: () => {
      // Implementation will be added when we create the actions
    },
  };
};

// ============================================================================
// PERFORMANCE HOOKS
// ============================================================================

/**
 * Hook for optimized state selection with shallow comparison
 */
export const useShallowSelector = <T>(selector: (state: RootState) => T) => {
  return useSelector(selector, (left, right) => {
    if (typeof left !== "object" || typeof right !== "object") {
      return left === right;
    }

    if (left === null || right === null) {
      return left === right;
    }

    const leftKeys = Object.keys(left);
    const rightKeys = Object.keys(right);

    if (leftKeys.length !== rightKeys.length) {
      return false;
    }

    for (const key of leftKeys) {
      if ((left as any)[key] !== (right as any)[key]) {
        return false;
      }
    }

    return true;
  });
};

/**
 * Hook for debounced state updates
 */
export const useDebouncedDispatch = (delay: number = 300) => {
  const dispatch = useAppDispatch();
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  return React.useCallback(
    (action: any) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        dispatch(action);
      }, delay);
    },
    [dispatch, delay]
  );
};

// ============================================================================
// ERROR HANDLING HOOKS
// ============================================================================

/**
 * Hook for error handling with automatic retry
 */
export const useAppDispatchWithRetry = (maxRetries: number = 3) => {
  const dispatch = useAppDispatch();

  return async (action: any, retryCount: number = 0): Promise<any> => {
    try {
      return await dispatch(action);
    } catch (error) {
      if (retryCount < maxRetries) {
        // Wait before retrying (exponential backoff)
        await new Promise((resolve) =>
          setTimeout(resolve, Math.pow(2, retryCount) * 1000)
        );
        return useAppDispatchWithRetry(maxRetries)(action, retryCount + 1);
      }
      throw error;
    }
  };
};

/**
 * Hook for safe state access with fallbacks
 */
export const useSafeSelector = <T>(
  selector: (state: RootState) => T,
  fallback: T
) => {
  return useSelector((state: RootState) => {
    try {
      return selector(state) ?? fallback;
    } catch (error) {
      console.warn("Selector error, using fallback:", error);
      return fallback;
    }
  });
};

// ============================================================================
// DEVELOPMENT HOOKS
// ============================================================================

/**
 * Hook for debugging state changes (development only)
 */
export const useStateDebugger = (sliceName?: string) => {
  const state = useAppSelector((state) =>
    sliceName ? (state as any)[sliceName] : state
  );

  React.useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log(`State change in ${sliceName || "root"}:`, state);
    }
  }, [state, sliceName]);

  return state;
};

/**
 * Hook for performance monitoring (development only)
 */
export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = React.useRef(0);
  const startTime = React.useRef(Date.now());

  React.useEffect(() => {
    renderCount.current += 1;

    if (process.env.NODE_ENV === "development") {
      const renderTime = Date.now() - startTime.current;
      console.log(
        `${componentName} render #${renderCount.current} took ${renderTime}ms`
      );
    }

    startTime.current = Date.now();
  });

  return {
    renderCount: renderCount.current,
    resetCounter: () => {
      renderCount.current = 0;
    },
  };
};

// Note: React import will be added when we set up the store
import React from "react";

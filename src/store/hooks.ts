import { useDispatch, useSelector } from "react-redux";
import type { RootState, AppDispatch } from "./index";

// ============================================================================
// TYPED REDUX HOOKS
// ============================================================================

/**
 * Typed version of useDispatch hook
 */
export const useAppDispatch = () => useDispatch<AppDispatch>();

/**
 * Typed version of useSelector hook
 */
export const useAppSelector = <TSelected>(
  selector: (state: RootState) => TSelected
) => useSelector(selector);

// ============================================================================
// PRODUCT DATA HOOKS
// ============================================================================

/**
 * Hook for accessing product data
 */
export const useProductData = () => {
  const strapiProduct = useAppSelector((state) => state.product.strapiProduct);
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);
  const isLoading = useAppSelector((state) => state.product.isLoading);
  const isError = useAppSelector((state) => state.product.isError);
  const error = useAppSelector((state) => state.product.error);

  return {
    strapiProduct,
    medusaProduct,
    isLoading,
    isError,
    error,
  };
};

/**
 * Hook for product data actions
 */
export const useProductDataActions = () => {
  const dispatch = useAppDispatch();

  return {
    initializeProduct: (data: { strapiProduct?: any; medusaProduct?: any }) => {
      // Import the action dynamically to avoid circular dependency
      import("./slices/productSlice").then(({ initializeProductData }) => {
        dispatch(initializeProductData(data));
      });
    },
  };
};

// ============================================================================
// CART HOOKS
// ============================================================================

/**
 * Hook for cart functionality
 */
export const useProductCart = () => {
  const dispatch = useAppDispatch();
  const quantity = useAppSelector((state) => state.cart.quantity);
  const isAddingToCart = useAppSelector((state) => state.cart.isAddingToCart);
  // const cartError = useAppSelector((state) => state.cart.cartError); // Available for future use

  const setQuantity = (newQuantity: number) => {
    import("./slices/cartSlice").then(({ setQuantity: setQuantityAction }) => {
      dispatch(setQuantityAction(newQuantity));
    });
  };

  const incrementQuantity = () => {
    import("./slices/cartSlice").then(
      ({ incrementQuantity: incrementAction }) => {
        dispatch(incrementAction());
      }
    );
  };

  const decrementQuantity = () => {
    import("./slices/cartSlice").then(
      ({ decrementQuantity: decrementAction }) => {
        dispatch(decrementAction());
      }
    );
  };

  const addToCart = async () => {
    // This will be implemented with proper cart logic
    console.log("Add to cart - Redux implementation");
  };

  return {
    quantity,
    setQuantity,
    incrementQuantity,
    decrementQuantity,
    addToCart,
    isAddingToCart,
    canAddToCart: true, // Will be computed based on state
  };
};

/**
 * Hook for cart button state
 */
export const useCartButtonState = () => {
  const isAddingToCart = useAppSelector((state) => state.cart.isAddingToCart);
  const activeVariant = useAppSelector((state) => state.product.activeVariant);

  let buttonText = "Add to Cart";
  let buttonDisabled = false;

  if (isAddingToCart) {
    buttonText = "Adding...";
    buttonDisabled = true;
  } else if (!activeVariant) {
    buttonText = "Select Variant";
    buttonDisabled = true;
  }

  return {
    buttonText,
    buttonDisabled,
    isAddingToCart,
  };
};

// ============================================================================
// THEME HOOKS
// ============================================================================

/**
 * Hook for theme functionality
 */
export const useProductTheme = () => {
  const primaryColor = useAppSelector((state) => state.theme.primaryColor);
  const backgroundColor = useAppSelector(
    (state) => state.theme.backgroundColor
  );
  const cssVariables = useAppSelector((state) => state.theme.cssVariables);

  return {
    primaryColor,
    backgroundColor,
    cssVariables,
    themeStyles: cssVariables as React.CSSProperties,
  };
};

/**
 * Hook for theme actions
 */
export const useThemeActions = () => {
  const dispatch = useAppDispatch();

  return {
    initializeFromProduct: (data: {
      primaryColor?: string;
      backgroundColor?: string;
    }) => {
      import("./slices/themeSlice").then(({ initializeThemeFromProduct }) => {
        dispatch(initializeThemeFromProduct(data));
      });
    },
  };
};

// ============================================================================
// UI HOOKS
// ============================================================================

/**
 * Hook for UI functionality
 */
export const useProductUI = () => {
  const dispatch = useAppDispatch();
  const currentSlide = useAppSelector((state) => state.ui.currentSlide);
  const isModalOpen = useAppSelector((state) => state.ui.isModalOpen);
  const selectedImageIndex = useAppSelector(
    (state) => state.ui.selectedImageIndex
  );
  const images = useAppSelector((state) => state.product.combinedImages);

  const setCurrentSlide = (slide: number) => {
    import("./slices/uiSlice").then(({ setCurrentSlide: setSlideAction }) => {
      dispatch(setSlideAction(slide));
    });
  };

  const openModal = (imageIndex?: number) => {
    import("./slices/uiSlice").then(({ openModal: openModalAction }) => {
      dispatch(openModalAction(imageIndex));
    });
  };

  const closeModal = () => {
    import("./slices/uiSlice").then(({ closeModal: closeModalAction }) => {
      dispatch(closeModalAction());
    });
  };

  return {
    currentSlide,
    setCurrentSlide,
    isModalOpen,
    selectedImageIndex,
    openModal,
    closeModal,
    images,
    hasImages: images.length > 0,
    imageCount: images.length,
  };
};

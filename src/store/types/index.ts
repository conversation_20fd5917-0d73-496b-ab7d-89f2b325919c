import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { ExtendedMedusaProductWithStrapiProduct, ExtendedVariant } from "@/types/Medusa/Product";

// ============================================================================
// REDUX STATE TYPES
// ============================================================================

/**
 * Product slice state interface
 */
export interface ProductState {
  // Core product data
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;
  
  // Variant management
  activeVariant: ExtendedVariant | null;
  
  // Product metadata and computed values
  combinedImages: string[];
  currentPrice: number;
  originalPrice: number;
  loyaltyPoints: number;
  discountPercentage: number;
  breadcrumbItems: Array<{ label: string; href?: string }>;
  
  // Loading and error states
  isLoading: boolean;
  isError: boolean;
  error: string | null;
}

/**
 * Cart slice state interface
 */
export interface CartState {
  // Cart data
  items: CartItem[];
  
  // Cart UI state
  quantity: number;
  isAddingToCart: boolean;
  isCartOpen: boolean;
  
  // Cart operations state
  lastAddedItem: CartItem | null;
  cartError: string | null;
}

/**
 * Theme slice state interface
 */
export interface ThemeState {
  // Color theming
  primaryColor: string;
  backgroundColor: string;
  
  // CSS custom properties
  cssVariables: Record<string, string>;
}

/**
 * UI slice state interface
 */
export interface UIState {
  // Modal states
  isModalOpen: boolean;
  selectedImageIndex: number;
  
  // Carousel states
  currentSlide: number;
  
  // Loading states
  isUILoading: boolean;
  
  // Error states
  uiError: string | null;
}

// ============================================================================
// CART TYPES
// ============================================================================

export interface CartItem {
  id: string;
  productId: string;
  variantId: string;
  quantity: number;
  price: number;
  title: string;
  image?: string;
  variant?: ExtendedVariant;
  product?: ExtendedMedusaProductWithStrapiProduct;
}

export interface AddToCartPayload {
  product: ExtendedMedusaProductWithStrapiProduct;
  variant: ExtendedVariant;
  quantity: number;
  totalPrice: number;
}

// ============================================================================
// ACTION PAYLOAD TYPES
// ============================================================================

export interface SetProductDataPayload {
  strapiProduct?: ProductDetailsType | null;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct | null;
}

export interface SetVariantPayload {
  variant: ExtendedVariant;
}

export interface SetQuantityPayload {
  quantity: number;
}

export interface SetSlidePayload {
  slide: number;
}

export interface SetImageIndexPayload {
  index: number;
}

export interface SetThemePayload {
  primaryColor?: string;
  backgroundColor?: string;
}

export interface SetErrorPayload {
  error: string | null;
}

// ============================================================================
// HOOK RETURN TYPES (for compatibility with existing hooks)
// ============================================================================

export interface UseProductDataReturn {
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;
  isLoading: boolean;
  isError: boolean;
  error: string | null;
}

export interface UseProductCartReturn {
  quantity: number;
  setQuantity: (quantity: number) => void;
  incrementQuantity: () => void;
  decrementQuantity: () => void;
  addToCart: () => Promise<void>;
  isAddingToCart: boolean;
  canAddToCart: boolean;
}

export interface UseProductUIReturn {
  currentSlide: number;
  setCurrentSlide: (slide: number) => void;
  isModalOpen: boolean;
  selectedImageIndex: number;
  openModal: (imageIndex?: number) => void;
  closeModal: () => void;
  images: string[];
  hasImages: boolean;
  imageCount: number;
}

export interface UseProductThemeReturn {
  primaryColor: string;
  backgroundColor: string;
  cssVariables: Record<string, string>;
  themeStyles: React.CSSProperties;
}

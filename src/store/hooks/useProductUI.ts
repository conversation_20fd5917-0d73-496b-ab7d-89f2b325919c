import { useCallback } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import { 
  selectCompleteUIState,
  selectModalState,
  selectCarouselState,
  selectImageNavigation,
  selectModalNavigation,
  selectCarouselNavigation,
  selectKeyboardNavigationState
} from '../selectors/uiSelectors';
import { 
  openModal,
  closeModal,
  setSelectedImageIndex,
  setCurrentSlide,
  nextSlide,
  previousSlide,
  goToSlide,
  nextModalImage,
  previousModalImage,
  goToModalImage,
  openModalAtSlide,
  closeModalAndSyncCarousel,
  syncModalWithCarousel,
  syncCarouselWithModal,
  handleKeyboardNavigation,
  setUIError,
  clearUIError
} from '../slices/uiSlice';
import { UseProductUIReturn } from '../types';

// ============================================================================
// MAIN PRODUCT UI HOOK
// ============================================================================

/**
 * Hook for managing product UI state
 * Provides carousel, modal, and other UI-related functionality with Redux state management
 */
export const useProductUI = (): UseProductUIReturn => {
  const dispatch = useAppDispatch();
  const uiState = useAppSelector(selectCompleteUIState);
  const imageNavigation = useAppSelector(selectImageNavigation);

  const setSlide = useCallback((slide: number) => {
    dispatch(setCurrentSlide(slide));
  }, [dispatch]);

  const openImageModal = useCallback((imageIndex?: number) => {
    if (imageIndex !== undefined) {
      dispatch(openModalAtSlide(imageIndex));
    } else {
      dispatch(openModal(undefined));
    }
  }, [dispatch]);

  const closeImageModal = useCallback(() => {
    dispatch(closeModal());
  }, [dispatch]);

  return {
    currentSlide: uiState.carousel.currentSlide,
    setCurrentSlide: setSlide,
    isModalOpen: uiState.modal.isModalOpen,
    selectedImageIndex: uiState.modal.selectedImageIndex,
    openModal: openImageModal,
    closeModal: closeImageModal,
    images: imageNavigation.images,
    hasImages: imageNavigation.hasImages,
    imageCount: imageNavigation.totalImages,
  };
};

// ============================================================================
// CAROUSEL NAVIGATION HOOK
// ============================================================================

/**
 * Hook for carousel navigation
 */
export const useCarouselNavigation = () => {
  const dispatch = useAppDispatch();
  const carouselState = useAppSelector(selectCarouselState);
  const navigation = useAppSelector(selectCarouselNavigation);
  const imageNavigation = useAppSelector(selectImageNavigation);

  const goToNext = useCallback(() => {
    dispatch(nextSlide(imageNavigation.totalImages));
  }, [dispatch, imageNavigation.totalImages]);

  const goToPrevious = useCallback(() => {
    dispatch(previousSlide(imageNavigation.totalImages));
  }, [dispatch, imageNavigation.totalImages]);

  const goToSlideIndex = useCallback((index: number) => {
    dispatch(goToSlide({ slideIndex: index, totalSlides: imageNavigation.totalImages }));
  }, [dispatch, imageNavigation.totalImages]);

  return {
    currentSlide: carouselState.currentSlide,
    canGoNext: navigation.canGoNext,
    canGoPrevious: navigation.canGoPrevious,
    goToNext,
    goToPrevious,
    goToSlide: goToSlideIndex,
    totalSlides: navigation.totalSlides,
    isFirstSlide: navigation.isFirstSlide,
    isLastSlide: navigation.isLastSlide,
  };
};

// ============================================================================
// MODAL MANAGEMENT HOOK
// ============================================================================

/**
 * Hook for modal management
 */
export const useProductModal = () => {
  const dispatch = useAppDispatch();
  const modalState = useAppSelector(selectModalState);
  const modalNavigation = useAppSelector(selectModalNavigation);
  const imageNavigation = useAppSelector(selectImageNavigation);

  const openImageModal = useCallback((imageIndex?: number) => {
    if (imageIndex !== undefined) {
      dispatch(openModalAtSlide(imageIndex));
    } else {
      dispatch(openModal(undefined));
    }
  }, [dispatch]);

  const closeImageModal = useCallback(() => {
    dispatch(closeModalAndSyncCarousel());
  }, [dispatch]);

  const goToNextImage = useCallback(() => {
    dispatch(nextModalImage(imageNavigation.totalImages));
  }, [dispatch, imageNavigation.totalImages]);

  const goToPreviousImage = useCallback(() => {
    dispatch(previousModalImage(imageNavigation.totalImages));
  }, [dispatch, imageNavigation.totalImages]);

  const goToImageIndex = useCallback((index: number) => {
    dispatch(goToModalImage({ imageIndex: index, totalImages: imageNavigation.totalImages }));
  }, [dispatch, imageNavigation.totalImages]);

  return {
    isModalOpen: modalState.isModalOpen,
    selectedImageIndex: modalState.selectedImageIndex,
    openModal: openImageModal,
    closeModal: closeImageModal,
    currentModalImage: modalState.currentModalImage,
    hasModalImage: modalState.hasModalImage,
    canGoNext: modalNavigation.canGoNext,
    canGoPrevious: modalNavigation.canGoPrevious,
    goToNext: goToNextImage,
    goToPrevious: goToPreviousImage,
    goToImage: goToImageIndex,
    isFirstImage: modalNavigation.isFirstImage,
    isLastImage: modalNavigation.isLastImage,
  };
};

// ============================================================================
// KEYBOARD NAVIGATION HOOK
// ============================================================================

/**
 * Hook for keyboard navigation support
 */
export const useKeyboardNavigation = () => {
  const dispatch = useAppDispatch();
  const keyboardState = useAppSelector(selectKeyboardNavigationState);

  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    // Prevent default behavior for navigation keys
    if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End', 'Escape'].includes(event.key)) {
      event.preventDefault();
    }

    dispatch(handleKeyboardNavigation({
      key: event.key,
      totalItems: keyboardState.totalItems,
      isModal: keyboardState.isModalOpen,
    }));
  }, [dispatch, keyboardState]);

  return {
    handleKeyPress,
    canNavigate: keyboardState.canNavigate,
    isModalOpen: keyboardState.isModalOpen,
    totalItems: keyboardState.totalItems,
    currentIndex: keyboardState.currentIndex,
  };
};

// ============================================================================
// UI SYNCHRONIZATION HOOK
// ============================================================================

/**
 * Hook for synchronizing UI state between carousel and modal
 */
export const useUISynchronization = () => {
  const dispatch = useAppDispatch();

  const syncModalToCarousel = useCallback(() => {
    dispatch(syncModalWithCarousel());
  }, [dispatch]);

  const syncCarouselToModal = useCallback(() => {
    dispatch(syncCarouselWithModal());
  }, [dispatch]);

  return {
    syncModalToCarousel,
    syncCarouselToModal,
  };
};

// ============================================================================
// UI ERROR HANDLING HOOK
// ============================================================================

/**
 * Hook for UI error handling
 */
export const useUIErrorHandling = () => {
  const dispatch = useAppDispatch();
  const uiError = useAppSelector((state) => state.ui.uiError);

  const setError = useCallback((error: string | null) => {
    dispatch(setUIError(error));
  }, [dispatch]);

  const clearError = useCallback(() => {
    dispatch(clearUIError());
  }, [dispatch]);

  const handleError = useCallback((error: Error | string) => {
    const errorMessage = error instanceof Error ? error.message : error;
    setError(errorMessage);
    console.error('UI error:', error);
  }, [setError]);

  return {
    uiError,
    hasError: !!uiError,
    setError,
    clearError,
    handleError,
  };
};

// ============================================================================
// RESPONSIVE UI HOOK
// ============================================================================

/**
 * Hook for responsive behavior
 */
export const useProductResponsive = () => {
  // This could be enhanced with actual breakpoint detection
  // For now, we'll provide the structure based on window size
  const [isMobile, setIsMobile] = React.useState(false);
  const [isTablet, setIsTablet] = React.useState(false);

  React.useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return {
    isMobile,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    showMobileCarousel: isMobile,
    showDesktopCarousel: !isMobile,
  };
};

// ============================================================================
// UI STATUS HOOK
// ============================================================================

/**
 * Hook for loading and error states
 */
export const useProductStatus = () => {
  const isUILoading = useAppSelector((state) => state.ui.isUILoading);
  const uiError = useAppSelector((state) => state.ui.uiError);
  const productLoading = useAppSelector((state) => state.product.isLoading);
  const productError = useAppSelector((state) => state.product.isError);

  return {
    isLoading: isUILoading || productLoading,
    isError: !!uiError || productError,
    error: uiError || null,
    isReady: !isUILoading && !productLoading && !uiError && !productError,
    hasError: !!uiError || productError,
  };
};

// ============================================================================
// COMPOSITE UI HOOK
// ============================================================================

/**
 * Hook that combines all UI functionality
 */
export const useCompleteProductUI = () => {
  const ui = useProductUI();
  const carousel = useCarouselNavigation();
  const modal = useProductModal();
  const keyboard = useKeyboardNavigation();
  const sync = useUISynchronization();
  const errorHandling = useUIErrorHandling();
  const responsive = useProductResponsive();
  const status = useProductStatus();

  return {
    // Basic UI functionality
    ...ui,
    
    // Carousel navigation
    carousel,
    
    // Modal management
    modal,
    
    // Keyboard navigation
    keyboard,
    
    // Synchronization
    sync,
    
    // Error handling
    errorHandling,
    
    // Responsive behavior
    responsive,
    
    // Status
    status,
  };
};

// Note: React import will be added
import React from 'react';

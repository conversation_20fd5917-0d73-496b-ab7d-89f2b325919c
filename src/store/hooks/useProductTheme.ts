import { useCallback } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import { 
  selectCompleteTheme,
  selectThemeStyles,
  selectPrimaryColor,
  selectBackgroundColor,
  selectCSSVariables,
  selectThemeValidation,
  selectCurrentThemePreset,
  selectAvailableThemePresets,
  selectButtonTheme,
  selectCardTheme,
  selectModalTheme
} from '../selectors/themeSelectors';
import { 
  setPrimaryColor,
  setBackgroundColor,
  setThemeColors,
  initializeThemeFromProduct,
  resetTheme,
  applyPredefinedTheme,
  updateCSSVariables,
  generateThemeVariations
} from '../slices/themeSlice';
import { UseProductThemeReturn } from '../types';

// ============================================================================
// MAIN PRODUCT THEME HOOK
// ============================================================================

/**
 * Hook for theme and styling
 * Provides theme-related functionality with Redux state management
 */
export const useProductTheme = (): UseProductThemeReturn => {
  const themeStyles = useAppSelector(selectThemeStyles);

  return {
    primaryColor: themeStyles.primaryColor,
    backgroundColor: themeStyles.backgroundColor,
    cssVariables: themeStyles.cssVariables,
    themeStyles: themeStyles.cssVariables,
  };
};

// ============================================================================
// THEME MANAGEMENT HOOKS
// ============================================================================

/**
 * Hook for theme management actions
 */
export const useThemeActions = () => {
  const dispatch = useAppDispatch();

  const updatePrimaryColor = useCallback((color: string) => {
    dispatch(setPrimaryColor(color));
    dispatch(generateThemeVariations());
  }, [dispatch]);

  const updateBackgroundColor = useCallback((color: string) => {
    dispatch(setBackgroundColor(color));
    dispatch(generateThemeVariations());
  }, [dispatch]);

  const updateThemeColors = useCallback((colors: { primaryColor?: string; backgroundColor?: string }) => {
    dispatch(setThemeColors(colors));
    dispatch(generateThemeVariations());
  }, [dispatch]);

  const initializeFromProduct = useCallback((productData: { primaryColor?: string; backgroundColor?: string }) => {
    dispatch(initializeThemeFromProduct(productData));
    dispatch(generateThemeVariations());
  }, [dispatch]);

  const resetToDefault = useCallback(() => {
    dispatch(resetTheme());
  }, [dispatch]);

  const applyPreset = useCallback((preset: 'default' | 'dark' | 'light' | 'green' | 'blue') => {
    dispatch(applyPredefinedTheme(preset));
    dispatch(generateThemeVariations());
  }, [dispatch]);

  const updateCustomVariables = useCallback((variables: Record<string, string>) => {
    dispatch(updateCSSVariables(variables));
  }, [dispatch]);

  return {
    setPrimaryColor: updatePrimaryColor,
    setBackgroundColor: updateBackgroundColor,
    setThemeColors: updateThemeColors,
    initializeFromProduct,
    resetTheme: resetToDefault,
    applyPreset,
    updateCSSVariables: updateCustomVariables,
  };
};

// ============================================================================
// THEME COLOR HOOKS
// ============================================================================

/**
 * Hook for accessing and managing primary color
 */
export const usePrimaryColor = () => {
  const primaryColor = useAppSelector(selectPrimaryColor);
  const dispatch = useAppDispatch();

  const setPrimary = useCallback((color: string) => {
    dispatch(setPrimaryColor(color));
    dispatch(generateThemeVariations());
  }, [dispatch]);

  return {
    primaryColor,
    setPrimaryColor: setPrimary,
  };
};

/**
 * Hook for accessing and managing background color
 */
export const useBackgroundColor = () => {
  const backgroundColor = useAppSelector(selectBackgroundColor);
  const dispatch = useAppDispatch();

  const setBackground = useCallback((color: string) => {
    dispatch(setBackgroundColor(color));
    dispatch(generateThemeVariations());
  }, [dispatch]);

  return {
    backgroundColor,
    setBackgroundColor: setBackground,
  };
};

// ============================================================================
// THEME VALIDATION HOOK
// ============================================================================

/**
 * Hook for theme validation
 */
export const useThemeValidation = () => {
  const validation = useAppSelector(selectThemeValidation);

  return validation;
};

// ============================================================================
// THEME PRESETS HOOK
// ============================================================================

/**
 * Hook for theme presets management
 */
export const useThemePresets = () => {
  const currentPreset = useAppSelector(selectCurrentThemePreset);
  const availablePresets = useAppSelector(selectAvailableThemePresets);
  const dispatch = useAppDispatch();

  const applyPreset = useCallback((preset: 'default' | 'dark' | 'light' | 'green' | 'blue') => {
    dispatch(applyPredefinedTheme(preset));
    dispatch(generateThemeVariations());
  }, [dispatch]);

  return {
    currentPreset,
    availablePresets,
    applyPreset,
  };
};

// ============================================================================
// COMPONENT THEME HOOKS
// ============================================================================

/**
 * Hook for button theme styles
 */
export const useButtonTheme = () => {
  const buttonTheme = useAppSelector(selectButtonTheme);

  return buttonTheme;
};

/**
 * Hook for card theme styles
 */
export const useCardTheme = () => {
  const cardTheme = useAppSelector(selectCardTheme);

  return cardTheme;
};

/**
 * Hook for modal theme styles
 */
export const useModalTheme = () => {
  const modalTheme = useAppSelector(selectModalTheme);

  return modalTheme;
};

// ============================================================================
// CSS VARIABLES HOOK
// ============================================================================

/**
 * Hook for CSS custom properties management
 */
export const useCSSVariables = () => {
  const cssVariables = useAppSelector(selectCSSVariables);
  const dispatch = useAppDispatch();

  const updateVariables = useCallback((variables: Record<string, string>) => {
    dispatch(updateCSSVariables(variables));
  }, [dispatch]);

  const applyVariablesToElement = useCallback((element: HTMLElement) => {
    Object.entries(cssVariables).forEach(([property, value]) => {
      element.style.setProperty(property, value);
    });
  }, [cssVariables]);

  const applyVariablesToDocument = useCallback(() => {
    const root = document.documentElement;
    Object.entries(cssVariables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }, [cssVariables]);

  return {
    cssVariables,
    updateVariables,
    applyVariablesToElement,
    applyVariablesToDocument,
  };
};

// ============================================================================
// THEME PERSISTENCE HOOK
// ============================================================================

/**
 * Hook for theme persistence operations
 */
export const useThemePersistence = () => {
  const theme = useAppSelector((state) => state.theme);

  const saveThemeToLocalStorage = useCallback(() => {
    // Redux-persist handles this automatically
    console.log('Theme will be automatically persisted');
  }, []);

  const loadThemeFromLocalStorage = useCallback(() => {
    // Redux-persist handles this automatically
    console.log('Theme will be automatically loaded on app start');
  }, []);

  const exportTheme = useCallback(() => {
    return {
      primaryColor: theme.primaryColor,
      backgroundColor: theme.backgroundColor,
      timestamp: Date.now(),
    };
  }, [theme]);

  const importTheme = useCallback((themeData: { primaryColor: string; backgroundColor: string }) => {
    const dispatch = useAppDispatch();
    dispatch(setThemeColors(themeData));
    dispatch(generateThemeVariations());
  }, []);

  return {
    saveThemeToLocalStorage,
    loadThemeFromLocalStorage,
    exportTheme,
    importTheme,
  };
};

// ============================================================================
// THEME ANIMATION HOOK
// ============================================================================

/**
 * Hook for theme transition animations
 */
export const useThemeAnimations = () => {
  const [isTransitioning, setIsTransitioning] = React.useState(false);

  const animateThemeChange = useCallback(async (
    newTheme: { primaryColor?: string; backgroundColor?: string },
    duration: number = 300
  ) => {
    setIsTransitioning(true);
    
    // Add transition class to document
    document.documentElement.style.transition = `all ${duration}ms ease-in-out`;
    
    // Apply new theme
    const dispatch = useAppDispatch();
    dispatch(setThemeColors(newTheme));
    dispatch(generateThemeVariations());
    
    // Remove transition after animation
    setTimeout(() => {
      document.documentElement.style.transition = '';
      setIsTransitioning(false);
    }, duration);
  }, []);

  return {
    isTransitioning,
    animateThemeChange,
  };
};

// ============================================================================
// COMPREHENSIVE THEME HOOK
// ============================================================================

/**
 * Hook that combines all theme functionality
 */
export const useCompleteTheme = () => {
  const theme = useProductTheme();
  const actions = useThemeActions();
  const validation = useThemeValidation();
  const presets = useThemePresets();
  const buttonTheme = useButtonTheme();
  const cardTheme = useCardTheme();
  const modalTheme = useModalTheme();
  const cssVariables = useCSSVariables();
  const persistence = useThemePersistence();

  return {
    // Basic theme functionality
    ...theme,
    
    // Theme actions
    ...actions,
    
    // Validation
    validation,
    
    // Presets
    presets,
    
    // Component themes
    components: {
      button: buttonTheme,
      card: cardTheme,
      modal: modalTheme,
    },
    
    // CSS variables
    cssVariables: cssVariables.cssVariables,
    updateCSSVariables: cssVariables.updateVariables,
    applyVariablesToElement: cssVariables.applyVariablesToElement,
    applyVariablesToDocument: cssVariables.applyVariablesToDocument,
    
    // Persistence
    persistence,
  };
};

// ============================================================================
// THEME CONTEXT COMPATIBILITY HOOK
// ============================================================================

/**
 * Hook that provides the same interface as the old context-based theme hook
 * for backward compatibility during migration
 */
export const useProductThemeCompat = () => {
  const theme = useProductTheme();
  const completeTheme = useCompleteTheme();

  return {
    // Original interface
    primaryColor: theme.primaryColor,
    backgroundColor: theme.backgroundColor,
    cssVariables: theme.cssVariables,
    themeStyles: theme.themeStyles,
    
    // Extended functionality
    ...completeTheme,
  };
};

// Note: React import will be added
import React from 'react';

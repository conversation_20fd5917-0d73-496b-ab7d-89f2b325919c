// ============================================================================
// REDUX HOOKS EXPORTS
// ============================================================================

// Core Redux hooks
export { useAppDispatch, useAppSelector } from '../hooks';

// ============================================================================
// PRODUCT DATA HOOKS
// ============================================================================

export {
  useProductData,
  useProductDataActions,
  useStrapiProduct,
  useMedusaProduct,
  useProductAvailability,
  useProductMetadata,
  useProductLoadingState,
  useCompleteProductData,
  useProductDataWithErrorHandling,
} from './useProductData';

// ============================================================================
// CART HOOKS
// ============================================================================

export {
  useProductCart,
  useProductCartActions,
  useQuantityConstraints,
  useCartButtonState,
  useCartMessages,
  useCartSummary,
  useCartValidation,
  useCartPersistence,
  useCompleteProductCart,
  useCartErrorHandling,
} from './useProductCart';

// ============================================================================
// UI HOOKS
// ============================================================================

export {
  useProductUI,
  useCarouselNavigation,
  useProductModal,
  useKeyboardNavigation,
  useUISynchronization,
  useUIErrorHandling,
  useProductResponsive,
  useProductStatus,
  useCompleteProductUI,
} from './useProductUI';

// ============================================================================
// THEME HOOKS
// ============================================================================

export {
  useProductTheme,
  useThemeActions,
  usePrimaryColor,
  useBackgroundColor,
  useThemeValidation,
  useThemePresets,
  useButtonTheme,
  useCardTheme,
  useModalTheme,
  useCSSVariables,
  useThemePersistence,
  useThemeAnimations,
  useCompleteTheme,
  useProductThemeCompat,
} from './useProductTheme';

// ============================================================================
// COMPOSITE HOOKS FOR COMPLETE FUNCTIONALITY
// ============================================================================

/**
 * Hook that provides all PDP functionality in one place
 * Use this for components that need access to everything
 */
export const useCompletePDP = () => {
  const productData = useCompleteProductData();
  const cart = useCompleteProductCart();
  const ui = useCompleteProductUI();
  const theme = useCompleteTheme();

  return {
    product: productData,
    cart,
    ui,
    theme,
  };
};

/**
 * Hook for backward compatibility with the old context API
 * Provides the same interface as the old useProduct hook
 */
export const useProductCompat = () => {
  const productData = useProductData();
  const productMetadata = useProductMetadata();
  const cart = useProductCart();
  const ui = useProductUI();
  const theme = useProductTheme();
  const cartActions = useProductCartActions();
  const themeActions = useThemeActions();
  const { dispatch } = useAppDispatch();

  // Computed values from selectors
  const combinedImages = ui.images;
  const currentPrice = useAppSelector((state) => state.product.currentPrice);
  const originalPrice = useAppSelector((state) => state.product.originalPrice);
  const loyaltyPoints = useAppSelector((state) => state.product.loyaltyPoints);
  const discountPercentage = useAppSelector((state) => state.product.discountPercentage);
  const breadcrumbItems = useAppSelector((state) => state.product.breadcrumbItems);
  const activeVariant = useAppSelector((state) => state.product.activeVariant);

  return {
    // Data
    strapiProduct: productData.strapiProduct,
    medusaProduct: productData.medusaProduct,
    
    // UI State
    activeVariant,
    quantity: cart.quantity,
    currentSlide: ui.currentSlide,
    isModalOpen: ui.isModalOpen,
    selectedImageIndex: ui.selectedImageIndex,
    
    // Theme
    primaryColor: theme.primaryColor,
    backgroundColor: theme.backgroundColor,
    
    // Loading & Error
    isLoading: productData.isLoading,
    isError: productData.isError,
    error: productData.error,
    
    // Computed Values
    combinedImages,
    currentPrice,
    originalPrice,
    loyaltyPoints,
    discountPercentage,
    breadcrumbItems,
    
    // Actions (maintaining the same interface)
    setActiveVariant: (variant: any) => {
      dispatch(setActiveVariant(variant));
    },
    setQuantity: cart.setQuantity,
    incrementQuantity: cart.incrementQuantity,
    decrementQuantity: cart.decrementQuantity,
    setCurrentSlide: ui.setCurrentSlide,
    openModal: ui.openModal,
    closeModal: ui.closeModal,
    addToCart: cart.addToCart,
    applyCoupon: () => {
      // Implement coupon logic if needed
      console.log('Apply coupon - to be implemented');
    },
  };
};

// ============================================================================
// SPECIALIZED HOOKS FOR SPECIFIC USE CASES
// ============================================================================

/**
 * Hook for components that only need product data
 */
export const useProductDataOnly = () => {
  const productData = useProductData();
  const metadata = useProductMetadata();
  const availability = useProductAvailability();

  return {
    ...productData,
    ...metadata,
    ...availability,
  };
};

/**
 * Hook for components that only need cart functionality
 */
export const useCartOnly = () => {
  const cart = useProductCart();
  const validation = useCartValidation();
  const messages = useCartMessages();

  return {
    ...cart,
    ...validation,
    ...messages,
  };
};

/**
 * Hook for components that only need UI state
 */
export const useUIOnly = () => {
  const ui = useProductUI();
  const carousel = useCarouselNavigation();
  const modal = useProductModal();

  return {
    ...ui,
    carousel,
    modal,
  };
};

/**
 * Hook for components that only need theme
 */
export const useThemeOnly = () => {
  const theme = useProductTheme();
  const actions = useThemeActions();

  return {
    ...theme,
    ...actions,
  };
};

// ============================================================================
// PERFORMANCE OPTIMIZED HOOKS
// ============================================================================

/**
 * Hook that only subscribes to specific product data changes
 */
export const useProductDataOptimized = () => {
  const strapiProduct = useAppSelector((state) => state.product.strapiProduct);
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);
  const activeVariant = useAppSelector((state) => state.product.activeVariant);
  const isLoading = useAppSelector((state) => state.product.isLoading);

  return {
    strapiProduct,
    medusaProduct,
    activeVariant,
    isLoading,
    hasData: !!(strapiProduct || medusaProduct),
  };
};

/**
 * Hook that only subscribes to cart state changes
 */
export const useCartStateOptimized = () => {
  const quantity = useAppSelector((state) => state.cart.quantity);
  const isAddingToCart = useAppSelector((state) => state.cart.isAddingToCart);
  const cartError = useAppSelector((state) => state.cart.cartError);

  return {
    quantity,
    isAddingToCart,
    cartError,
    hasError: !!cartError,
  };
};

// ============================================================================
// IMPORTS FOR INTERNAL USE
// ============================================================================

import { useAppSelector, useAppDispatch } from '../hooks';
import { setActiveVariant } from '../slices/productSlice';
import {
  useCompleteProductData,
  useProductData,
  useProductMetadata,
  useProductAvailability,
} from './useProductData';
import {
  useCompleteProductCart,
  useProductCart,
  useCartValidation,
  useCartMessages,
  useProductCartActions,
} from './useProductCart';
import {
  useCompleteProductUI,
  useProductUI,
  useCarouselNavigation,
  useProductModal,
} from './useProductUI';
import {
  useCompleteTheme,
  useProductTheme,
  useThemeActions,
} from './useProductTheme';

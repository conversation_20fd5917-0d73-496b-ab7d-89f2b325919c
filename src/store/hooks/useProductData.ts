import { useCallback } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import { 
  selectProductData, 
  selectProductMetadata, 
  selectLoadingState,
  selectHasProductData,
  selectHasStrapiData,
  selectHasMedusaData,
  selectHasCompleteData,
  selectIsProductReady
} from '../selectors/productSelectors';
import { 
  setStrapiProduct, 
  setMedusaProduct, 
  initializeProductData,
  setError,
  clearError,
  setLoading
} from '../slices/productSlice';
import { initializeThemeFromProduct } from '../slices/themeSlice';
import { ProductDetailsType } from '@/types/Collections/ProductDetails';
import { ExtendedMedusaProductWithStrapiProduct } from '@/types/Medusa/Product';
import { UseProductDataReturn } from '../types';

// ============================================================================
// MAIN PRODUCT DATA HOOK
// ============================================================================

/**
 * Hook for accessing combined product data from both Strapi and Medusa
 * Provides a unified interface for product information with Redux state management
 */
export const useProductData = (): UseProductDataReturn => {
  const productData = useAppSelector(selectProductData);
  const loadingState = useAppSelector(selectLoadingState);

  return {
    strapiProduct: productData.strapiProduct,
    medusaProduct: productData.medusaProduct,
    isLoading: loadingState.isLoading,
    isError: loadingState.isError,
    error: loadingState.error,
  };
};

// ============================================================================
// PRODUCT DATA MANAGEMENT HOOKS
// ============================================================================

/**
 * Hook for managing product data with actions
 */
export const useProductDataActions = () => {
  const dispatch = useAppDispatch();

  const setStrapiProductData = useCallback((product: ProductDetailsType | null) => {
    dispatch(setStrapiProduct(product));
    
    // Initialize theme from product data if available
    if (product?.primary_color || product?.bg_color) {
      dispatch(initializeThemeFromProduct({
        primaryColor: product.primary_color,
        backgroundColor: product.bg_color,
      }));
    }
  }, [dispatch]);

  const setMedusaProductData = useCallback((product: ExtendedMedusaProductWithStrapiProduct | null) => {
    dispatch(setMedusaProduct(product));
  }, [dispatch]);

  const initializeProduct = useCallback(async (data: {
    strapiProduct?: ProductDetailsType | null;
    medusaProduct?: ExtendedMedusaProductWithStrapiProduct | null;
  }) => {
    try {
      await dispatch(initializeProductData(data)).unwrap();
      
      // Initialize theme from Strapi product data if available
      if (data.strapiProduct?.primary_color || data.strapiProduct?.bg_color) {
        dispatch(initializeThemeFromProduct({
          primaryColor: data.strapiProduct.primary_color,
          backgroundColor: data.strapiProduct.bg_color,
        }));
      }
    } catch (error) {
      console.error('Failed to initialize product data:', error);
    }
  }, [dispatch]);

  const setProductError = useCallback((error: string | null) => {
    dispatch(setError(error));
  }, [dispatch]);

  const clearProductError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const setProductLoading = useCallback((loading: boolean) => {
    dispatch(setLoading(loading));
  }, [dispatch]);

  return {
    setStrapiProduct: setStrapiProductData,
    setMedusaProduct: setMedusaProductData,
    initializeProduct,
    setError: setProductError,
    clearError: clearProductError,
    setLoading: setProductLoading,
  };
};

// ============================================================================
// SPECIFIC PRODUCT DATA HOOKS
// ============================================================================

/**
 * Hook for accessing Strapi product data specifically
 */
export const useStrapiProduct = () => {
  const strapiProduct = useAppSelector((state) => state.product.strapiProduct);
  const dispatch = useAppDispatch();

  const setStrapiProductData = useCallback((product: ProductDetailsType | null) => {
    dispatch(setStrapiProduct(product));
  }, [dispatch]);

  return {
    strapiProduct,
    setStrapiProduct: setStrapiProductData,
    hasData: !!strapiProduct,
  };
};

/**
 * Hook for accessing Medusa product data specifically
 */
export const useMedusaProduct = () => {
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);
  const dispatch = useAppDispatch();

  const setMedusaProductData = useCallback((product: ExtendedMedusaProductWithStrapiProduct | null) => {
    dispatch(setMedusaProduct(product));
  }, [dispatch]);

  return {
    medusaProduct,
    setMedusaProduct: setMedusaProductData,
    hasData: !!medusaProduct,
  };
};

// ============================================================================
// PRODUCT AVAILABILITY HOOKS
// ============================================================================

/**
 * Hook for checking if product data is available
 */
export const useProductAvailability = () => {
  const hasData = useAppSelector(selectHasProductData);
  const hasStrapiData = useAppSelector(selectHasStrapiData);
  const hasMedusaData = useAppSelector(selectHasMedusaData);
  const hasCompleteData = useAppSelector(selectHasCompleteData);
  const isReady = useAppSelector(selectIsProductReady);

  return {
    hasData,
    hasStrapiData,
    hasMedusaData,
    hasCompleteData,
    isReady,
  };
};

// ============================================================================
// PRODUCT METADATA HOOKS
// ============================================================================

/**
 * Hook for accessing product metadata
 */
export const useProductMetadata = () => {
  const metadata = useAppSelector(selectProductMetadata);

  return metadata;
};

// ============================================================================
// PRODUCT LOADING STATE HOOKS
// ============================================================================

/**
 * Hook for managing product loading states
 */
export const useProductLoadingState = () => {
  const loadingState = useAppSelector(selectLoadingState);
  const dispatch = useAppDispatch();

  const setLoading = useCallback((loading: boolean) => {
    dispatch(setLoading(loading));
  }, [dispatch]);

  const setError = useCallback((error: string | null) => {
    dispatch(setError(error));
  }, [dispatch]);

  const clearError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  return {
    ...loadingState,
    setLoading,
    setError,
    clearError,
  };
};

// ============================================================================
// COMPOSITE HOOKS
// ============================================================================

/**
 * Hook that combines product data with common actions
 */
export const useCompleteProductData = () => {
  const productData = useProductData();
  const productActions = useProductDataActions();
  const availability = useProductAvailability();
  const metadata = useProductMetadata();
  const loadingState = useProductLoadingState();

  return {
    // Data
    ...productData,
    ...metadata,
    
    // Availability
    ...availability,
    
    // Loading state
    ...loadingState,
    
    // Actions
    ...productActions,
  };
};

/**
 * Hook for product data with automatic error handling
 */
export const useProductDataWithErrorHandling = () => {
  const productData = useProductData();
  const { setError, clearError } = useProductDataActions();

  const handleError = useCallback((error: Error | string) => {
    const errorMessage = error instanceof Error ? error.message : error;
    setError(errorMessage);
    console.error('Product data error:', error);
  }, [setError]);

  const handleSuccess = useCallback(() => {
    clearError();
  }, [clearError]);

  return {
    ...productData,
    handleError,
    handleSuccess,
  };
};

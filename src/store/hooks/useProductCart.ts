import { useCallback } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import { 
  selectCompleteCartState,
  selectCanAddToCart,
  selectCartButtonState,
  selectCartMessages,
  selectQuantityConstraints,
  selectCartSummary
} from '../selectors/cartSelectors';
import { 
  setQuantity,
  incrementQuantity,
  decrementQuantity,
  addToCartAsync,
  setIsAddingToCart,
  clearCartError
} from '../slices/cartSlice';
import { UseProductCartReturn } from '../types';

// ============================================================================
// MAIN PRODUCT CART HOOK
// ============================================================================

/**
 * Hook for managing cart operations and quantity
 * Provides cart-related functionality for the product with Redux state management
 */
export const useProductCart = (): UseProductCartReturn => {
  const dispatch = useAppDispatch();
  const cartState = useAppSelector(selectCompleteCartState);
  const productState = useAppSelector((state) => state.product);

  const setCartQuantity = useCallback((quantity: number) => {
    if (quantity >= 1) {
      dispatch(setQuantity(quantity));
    }
  }, [dispatch]);

  const incrementCartQuantity = useCallback(() => {
    dispatch(incrementQuantity());
  }, [dispatch]);

  const decrementCartQuantity = useCallback(() => {
    dispatch(decrementQuantity());
  }, [dispatch]);

  const addToCart = useCallback(async () => {
    if (!cartState.canAddToCart || !productState.medusaProduct || !productState.activeVariant) {
      return;
    }

    try {
      await dispatch(addToCartAsync({
        product: productState.medusaProduct,
        variant: productState.activeVariant,
        quantity: cartState.quantity,
        totalPrice: productState.currentPrice * cartState.quantity,
      })).unwrap();
    } catch (error) {
      console.error('Failed to add to cart:', error);
    }
  }, [dispatch, cartState.canAddToCart, cartState.quantity, productState]);

  return {
    quantity: cartState.quantity,
    setQuantity: setCartQuantity,
    incrementQuantity: incrementCartQuantity,
    decrementQuantity: decrementCartQuantity,
    addToCart,
    isAddingToCart: cartState.isAddingToCart,
    canAddToCart: cartState.canAddToCart,
  };
};

// ============================================================================
// CART ACTIONS HOOK
// ============================================================================

/**
 * Hook for cart actions with external handlers
 */
export const useProductCartActions = () => {
  const dispatch = useAppDispatch();
  const productState = useAppSelector((state) => state.product);
  const cartState = useAppSelector((state) => state.cart);

  const addToCartWithHandler = useCallback(async (
    onAddToCart?: (data: any) => Promise<void>
  ) => {
    if (!productState.medusaProduct || !productState.activeVariant) {
      console.error('Cannot add to cart: missing product or variant data');
      return;
    }

    try {
      await dispatch(addToCartAsync({
        product: productState.medusaProduct,
        variant: productState.activeVariant,
        quantity: cartState.quantity,
        totalPrice: productState.currentPrice * cartState.quantity,
        onAddToCart,
      })).unwrap();
    } catch (error) {
      console.error('Failed to add to cart:', error);
    }
  }, [dispatch, productState, cartState.quantity]);

  const setAddingToCart = useCallback((isAdding: boolean) => {
    dispatch(setIsAddingToCart(isAdding));
  }, [dispatch]);

  const clearError = useCallback(() => {
    dispatch(clearCartError());
  }, [dispatch]);

  return {
    addToCartWithHandler,
    setAddingToCart,
    clearError,
  };
};

// ============================================================================
// QUANTITY CONSTRAINTS HOOK
// ============================================================================

/**
 * Hook for quantity validation and constraints
 */
export const useQuantityConstraints = () => {
  const constraints = useAppSelector(selectQuantityConstraints);

  return constraints;
};

// ============================================================================
// CART BUTTON STATE HOOK
// ============================================================================

/**
 * Hook for cart button states and messages
 */
export const useCartButtonState = () => {
  const buttonState = useAppSelector(selectCartButtonState);

  return buttonState;
};

// ============================================================================
// CART MESSAGES HOOK
// ============================================================================

/**
 * Hook for cart-related notifications and messages
 */
export const useCartMessages = () => {
  const messages = useAppSelector(selectCartMessages);

  return messages;
};

// ============================================================================
// CART SUMMARY HOOK
// ============================================================================

/**
 * Hook for cart summary information
 */
export const useCartSummary = () => {
  const summary = useAppSelector(selectCartSummary);

  return summary;
};

// ============================================================================
// CART VALIDATION HOOK
// ============================================================================

/**
 * Hook for cart validation logic
 */
export const useCartValidation = () => {
  const canAddToCart = useAppSelector(selectCanAddToCart);
  const constraints = useAppSelector(selectQuantityConstraints);
  const productState = useAppSelector((state) => state.product);

  const validateAddToCart = useCallback(() => {
    const errors: string[] = [];

    if (!productState.medusaProduct) {
      errors.push('Product data is not available');
    }

    if (!productState.activeVariant) {
      errors.push('Please select a product variant');
    }

    if (!constraints.hasStock) {
      errors.push('Product is out of stock');
    }

    if (!constraints.isValidQuantity) {
      errors.push('Invalid quantity selected');
    }

    if (constraints.quantity > constraints.maxQuantity) {
      errors.push(`Maximum quantity available is ${constraints.maxQuantity}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      canAddToCart,
    };
  }, [productState, constraints, canAddToCart]);

  return {
    validateAddToCart,
    canAddToCart,
    ...constraints,
  };
};

// ============================================================================
// CART PERSISTENCE HOOK
// ============================================================================

/**
 * Hook for cart persistence operations
 */
export const useCartPersistence = () => {
  const dispatch = useAppDispatch();

  const saveCartToStorage = useCallback(() => {
    // Redux-persist handles this automatically
    console.log('Cart will be automatically persisted');
  }, []);

  const loadCartFromStorage = useCallback(() => {
    // Redux-persist handles this automatically
    console.log('Cart will be automatically loaded on app start');
  }, []);

  const clearPersistedCart = useCallback(() => {
    // This would require implementing a clear action
    console.log('Clear persisted cart - to be implemented');
  }, []);

  return {
    saveCartToStorage,
    loadCartFromStorage,
    clearPersistedCart,
  };
};

// ============================================================================
// COMPOSITE CART HOOK
// ============================================================================

/**
 * Hook that combines all cart functionality
 */
export const useCompleteProductCart = () => {
  const cart = useProductCart();
  const actions = useProductCartActions();
  const constraints = useQuantityConstraints();
  const buttonState = useCartButtonState();
  const messages = useCartMessages();
  const summary = useCartSummary();
  const validation = useCartValidation();

  return {
    // Basic cart functionality
    ...cart,
    
    // Additional actions
    ...actions,
    
    // Constraints and validation
    ...constraints,
    ...validation,
    
    // UI state
    ...buttonState,
    
    // Messages and notifications
    ...messages,
    
    // Summary information
    ...summary,
  };
};

// ============================================================================
// CART ERROR HANDLING HOOK
// ============================================================================

/**
 * Hook for cart error handling with automatic retry
 */
export const useCartErrorHandling = () => {
  const dispatch = useAppDispatch();
  const cartError = useAppSelector((state) => state.cart.cartError);

  const handleCartError = useCallback((error: Error | string) => {
    const errorMessage = error instanceof Error ? error.message : error;
    console.error('Cart error:', errorMessage);
    // Could dispatch an error action here if needed
  }, []);

  const retryLastAction = useCallback(() => {
    // Implementation would depend on storing the last action
    console.log('Retry last cart action - to be implemented');
  }, []);

  const clearError = useCallback(() => {
    dispatch(clearCartError());
  }, [dispatch]);

  return {
    cartError,
    hasError: !!cartError,
    handleCartError,
    retryLastAction,
    clearError,
  };
};

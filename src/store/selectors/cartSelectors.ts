import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "../index";

// ============================================================================
// BASE SELECTORS
// ============================================================================

const selectCartState = (state: RootState) => state.cart;
const selectProductState = (state: RootState) => state.product;

// ============================================================================
// BASIC CART DATA SELECTORS
// ============================================================================

export const selectCartItems = createSelector(
  [selectCartState],
  (cart) => cart.items
);

export const selectCartQuantity = createSelector(
  [selectCartState],
  (cart) => cart.quantity
);

export const selectIsAddingToCart = createSelector(
  [selectCartState],
  (cart) => cart.isAddingToCart
);

export const selectIsCartOpen = createSelector(
  [selectCartState],
  (cart) => cart.isCartOpen
);

export const selectLastAddedItem = createSelector(
  [selectCartState],
  (cart) => cart.lastAddedItem
);

export const selectCartError = createSelector(
  [selectCartState],
  (cart) => cart.cartError
);

// ============================================================================
// COMPUTED CART SELECTORS
// ============================================================================

export const selectTotalCartItems = createSelector([selectCartItems], (items) =>
  items.reduce((total, item) => total + item.quantity, 0)
);

export const selectTotalCartPrice = createSelector([selectCartItems], (items) =>
  items.reduce((total, item) => total + item.price, 0)
);

export const selectFormattedTotalPrice = createSelector(
  [selectTotalCartPrice],
  (totalPrice) => `₹${totalPrice.toLocaleString()}`
);

export const selectCartItemCount = createSelector(
  [selectCartItems],
  (items) => items.length
);

export const selectIsCartEmpty = createSelector(
  [selectCartItems],
  (items) => items.length === 0
);

export const selectHasCartItems = createSelector(
  [selectCartItems],
  (items) => items.length > 0
);

// ============================================================================
// CART ITEM LOOKUP SELECTORS
// ============================================================================

export const selectCartItemById = createSelector(
  [selectCartItems, (state: RootState, itemId: string) => itemId],
  (items, itemId) => items.find((item) => item.id === itemId)
);

export const selectCartItemsByProductId = createSelector(
  [selectCartItems, (state: RootState, productId: string) => productId],
  (items, productId) => items.filter((item) => item.productId === productId)
);

export const selectCartItemByProductAndVariant = createSelector(
  [
    selectCartItems,
    (state: RootState, productId: string, variantId: string) => ({
      productId,
      variantId,
    }),
  ],
  (items, { productId, variantId }) =>
    items.find(
      (item) => item.productId === productId && item.variantId === variantId
    )
);

// ============================================================================
// CART VALIDATION SELECTORS
// ============================================================================

export const selectCanAddToCart = createSelector(
  [selectProductState, selectIsAddingToCart, selectCartQuantity],
  (product, isAddingToCart, quantity) => {
    const inventoryQuantity = product.activeVariant?.inventory_quantity ?? 0;
    return !!(
      product.medusaProduct &&
      product.activeVariant &&
      inventoryQuantity > 0 &&
      quantity > 0 &&
      quantity <= inventoryQuantity &&
      !product.isLoading &&
      !isAddingToCart
    );
  }
);

export const selectQuantityConstraints = createSelector(
  [selectProductState, selectCartQuantity],
  (product, quantity) => {
    const maxQuantity = product.activeVariant?.inventory_quantity ?? 0;
    const minQuantity = 1;

    return {
      minQuantity,
      maxQuantity,
      isAtMin: quantity <= minQuantity,
      isAtMax: quantity >= maxQuantity,
      isValidQuantity: quantity >= minQuantity && quantity <= maxQuantity,
      availableStock: maxQuantity,
      hasStock: maxQuantity > 0,
    };
  }
);

export const selectCartButtonState = createSelector(
  [
    selectCanAddToCart,
    selectIsAddingToCart,
    selectProductState,
    selectQuantityConstraints,
  ],
  (canAddToCart, isAddingToCart, product, { hasStock, isValidQuantity }) => {
    let buttonText = "Add to Cart";
    let buttonDisabled = false;
    let buttonVariant: "default" | "destructive" | "outline" | "secondary" =
      "default";

    if (isAddingToCart) {
      buttonText = "Adding...";
      buttonDisabled = true;
    } else if (!product.activeVariant) {
      buttonText = "Select Variant";
      buttonDisabled = true;
      buttonVariant = "outline";
    } else if (!hasStock) {
      buttonText = "Out of Stock";
      buttonDisabled = true;
      buttonVariant = "destructive";
    } else if (!isValidQuantity) {
      buttonText = "Invalid Quantity";
      buttonDisabled = true;
      buttonVariant = "destructive";
    } else if (!canAddToCart) {
      buttonText = "Cannot Add to Cart";
      buttonDisabled = true;
      buttonVariant = "outline";
    }

    return {
      buttonText,
      buttonDisabled,
      buttonVariant,
      canAddToCart,
      isAddingToCart,
    };
  }
);

// ============================================================================
// CART MESSAGES SELECTORS
// ============================================================================

export const selectCartMessages = createSelector(
  [selectProductState, selectQuantityConstraints],
  (product, { availableStock, hasStock }) => {
    const messages: string[] = [];

    if (!hasStock) {
      messages.push("This item is currently out of stock");
    } else if (availableStock <= 5) {
      messages.push(`Only ${availableStock} items left in stock`);
    }

    if (product.activeVariant?.inventory_quantity && availableStock <= 10) {
      messages.push("Order soon - limited stock available");
    }

    return {
      messages,
      hasMessages: messages.length > 0,
      stockWarning: availableStock <= 5 && hasStock,
      urgencyMessage: availableStock <= 10 && hasStock,
    };
  }
);

// ============================================================================
// CART ANALYTICS SELECTORS
// ============================================================================

export const selectCartAnalytics = createSelector(
  [selectCartItems, selectTotalCartItems, selectTotalCartPrice],
  (items, totalItems, totalPrice) => {
    const uniqueProducts = new Set(items.map((item) => item.productId)).size;
    const averageItemPrice = items.length > 0 ? totalPrice / totalItems : 0;
    const mostExpensiveItem = items.reduce(
      (max, item) =>
        item.price / item.quantity > max.price / max.quantity ? item : max,
      items[0] || { price: 0, quantity: 1 }
    );
    const leastExpensiveItem = items.reduce(
      (min, item) =>
        item.price / item.quantity < min.price / min.quantity ? item : min,
      items[0] || { price: 0, quantity: 1 }
    );

    return {
      totalItems,
      totalPrice,
      uniqueProducts,
      averageItemPrice,
      mostExpensiveItem: items.length > 0 ? mostExpensiveItem : null,
      leastExpensiveItem: items.length > 0 ? leastExpensiveItem : null,
    };
  }
);

// ============================================================================
// CART PERSISTENCE SELECTORS
// ============================================================================

export const selectCartForPersistence = createSelector(
  [selectCartItems, selectCartQuantity],
  (items, quantity) => ({
    items: items.map((item) => ({
      id: item.id,
      productId: item.productId,
      variantId: item.variantId,
      quantity: item.quantity,
      price: item.price,
      title: item.title,
      image: item.image,
    })),
    quantity,
    timestamp: Date.now(),
  })
);

// ============================================================================
// COMPREHENSIVE CART SELECTORS
// ============================================================================

export const selectCartSummary = createSelector(
  [
    selectCartItems,
    selectTotalCartItems,
    selectTotalCartPrice,
    selectFormattedTotalPrice,
    selectCartItemCount,
    selectIsCartEmpty,
    selectHasCartItems,
    selectIsAddingToCart,
    selectCartError,
  ],
  (
    items,
    totalItems,
    totalPrice,
    formattedTotalPrice,
    itemCount,
    isEmpty,
    hasItems,
    isAddingToCart,
    cartError
  ) => ({
    items,
    totalItems,
    totalPrice,
    formattedTotalPrice,
    itemCount,
    isEmpty,
    hasItems,
    isAddingToCart,
    cartError,
    hasError: !!cartError,
  })
);

export const selectCompleteCartState = createSelector(
  [
    selectCartSummary,
    selectCanAddToCart,
    selectCartButtonState,
    selectCartMessages,
    selectQuantityConstraints,
  ],
  (summary, canAddToCart, buttonState, messages, constraints) => ({
    ...summary,
    canAddToCart,
    ...buttonState,
    ...messages,
    ...constraints,
  })
);

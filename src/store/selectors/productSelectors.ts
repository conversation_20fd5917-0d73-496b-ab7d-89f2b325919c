import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "../index";

// ============================================================================
// BASE SELECTORS
// ============================================================================

const selectProductState = (state: RootState) => state.product;

// ============================================================================
// BASIC PRODUCT DATA SELECTORS
// ============================================================================

export const selectStrapiProduct = createSelector(
  [selectProductState],
  (product) => product.strapiProduct
);

export const selectMedusaProduct = createSelector(
  [selectProductState],
  (product) => product.medusaProduct
);

export const selectActiveVariant = createSelector(
  [selectProductState],
  (product) => product.activeVariant
);

export const selectCombinedImages = createSelector(
  [selectProductState],
  (product) => product.combinedImages
);

export const selectBreadcrumbItems = createSelector(
  [selectProductState],
  (product) => product.breadcrumbItems
);

// ============================================================================
// PRICING SELECTORS
// ============================================================================

export const selectCurrentPrice = createSelector(
  [selectProductState],
  (product) => product.currentPrice
);

export const selectOriginalPrice = createSelector(
  [selectProductState],
  (product) => product.originalPrice
);

export const selectDiscountPercentage = createSelector(
  [selectProductState],
  (product) => product.discountPercentage
);

export const selectLoyaltyPoints = createSelector(
  [selectProductState],
  (product) => product.loyaltyPoints
);

export const selectFormattedCurrentPrice = createSelector(
  [selectCurrentPrice],
  (currentPrice) => `₹${currentPrice.toLocaleString()}`
);

export const selectFormattedOriginalPrice = createSelector(
  [selectOriginalPrice],
  (originalPrice) => `₹${originalPrice.toLocaleString()}`
);

export const selectHasDiscount = createSelector(
  [selectDiscountPercentage],
  (discountPercentage) => discountPercentage > 0
);

export const selectPricingData = createSelector(
  [
    selectCurrentPrice,
    selectOriginalPrice,
    selectDiscountPercentage,
    selectLoyaltyPoints,
    selectFormattedCurrentPrice,
    selectFormattedOriginalPrice,
    selectHasDiscount,
  ],
  (
    currentPrice,
    originalPrice,
    discountPercentage,
    loyaltyPoints,
    formattedCurrentPrice,
    formattedOriginalPrice,
    hasDiscount
  ) => ({
    currentPrice,
    originalPrice,
    discountPercentage,
    loyaltyPoints,
    formattedCurrentPrice,
    formattedOriginalPrice,
    hasDiscount,
  })
);

// ============================================================================
// PRODUCT AVAILABILITY SELECTORS
// ============================================================================

export const selectHasProductData = createSelector(
  [selectStrapiProduct, selectMedusaProduct],
  (strapiProduct, medusaProduct) => !!(strapiProduct || medusaProduct)
);

export const selectHasStrapiData = createSelector(
  [selectStrapiProduct],
  (strapiProduct) => !!strapiProduct
);

export const selectHasMedusaData = createSelector(
  [selectMedusaProduct],
  (medusaProduct) => !!medusaProduct
);

export const selectHasCompleteData = createSelector(
  [selectStrapiProduct, selectMedusaProduct],
  (strapiProduct, medusaProduct) => !!(strapiProduct && medusaProduct)
);

export const selectIsProductReady = createSelector(
  [selectProductState, selectHasProductData],
  (product, hasData) => !product.isLoading && !product.isError && hasData
);

// ============================================================================
// PRODUCT METADATA SELECTORS
// ============================================================================

export const selectProductTitle = createSelector(
  [selectMedusaProduct, selectStrapiProduct],
  (medusaProduct, strapiProduct) =>
    medusaProduct?.title || strapiProduct?.title || "Product"
);

export const selectProductHandle = createSelector(
  [selectMedusaProduct],
  (medusaProduct) => medusaProduct?.handle || ""
);

export const selectProductDescription = createSelector(
  [selectMedusaProduct, selectStrapiProduct],
  (medusaProduct, strapiProduct) =>
    medusaProduct?.description || strapiProduct?.short_description || ""
);

export const selectProductShortDescription = createSelector(
  [selectStrapiProduct],
  (strapiProduct) => strapiProduct?.short_description || ""
);

export const selectProductAdditionalDescription = createSelector(
  [selectStrapiProduct],
  (strapiProduct) => strapiProduct?.additional_description || ""
);

export const selectProductTags = createSelector(
  [selectMedusaProduct],
  (medusaProduct) => medusaProduct?.tags || []
);

export const selectProductCategories = createSelector(
  [selectMedusaProduct],
  (medusaProduct) => medusaProduct?.categories || []
);

export const selectProductCollection = createSelector(
  [selectMedusaProduct],
  (medusaProduct) => medusaProduct?.collection || null
);

export const selectProductMetadata = createSelector(
  [
    selectProductTitle,
    selectProductHandle,
    selectProductDescription,
    selectProductShortDescription,
    selectProductAdditionalDescription,
    selectProductTags,
    selectProductCategories,
    selectProductCollection,
  ],
  (
    title,
    handle,
    description,
    shortDescription,
    additionalDescription,
    tags,
    categories,
    collection
  ) => ({
    title,
    handle,
    description,
    shortDescription,
    additionalDescription,
    tags,
    categories,
    collection,
  })
);

// ============================================================================
// VARIANT SELECTORS
// ============================================================================

export const selectProductVariants = createSelector(
  [selectMedusaProduct],
  (medusaProduct) => medusaProduct?.variants || []
);

export const selectHasVariants = createSelector(
  [selectProductVariants],
  (variants) => variants.length > 0
);

export const selectVariantAvailability = createSelector(
  [selectActiveVariant],
  (activeVariant) => {
    if (!activeVariant) return { hasStock: false, inventoryQuantity: 0 };

    const inventoryQuantity = activeVariant.inventory_quantity ?? 0;
    return {
      hasStock: inventoryQuantity > 0,
      inventoryQuantity,
      isLowStock: inventoryQuantity > 0 && inventoryQuantity <= 5,
      isOutOfStock: inventoryQuantity <= 0,
    };
  }
);

// ============================================================================
// IMAGE SELECTORS
// ============================================================================

export const selectHasImages = createSelector(
  [selectCombinedImages],
  (images) => images.length > 0
);

export const selectImageCount = createSelector(
  [selectCombinedImages],
  (images) => images.length
);

export const selectCurrentImage = createSelector(
  [selectCombinedImages, (state: RootState) => state.ui.currentSlide],
  (images, currentSlide) => images[currentSlide] || images[0] || ""
);

export const selectImageData = createSelector(
  [selectCombinedImages, selectCurrentImage, selectHasImages, selectImageCount],
  (images, currentImage, hasImages, imageCount) => ({
    images,
    currentImage,
    hasImages,
    imageCount,
  })
);

// ============================================================================
// LOADING AND ERROR SELECTORS
// ============================================================================

export const selectIsLoading = createSelector(
  [selectProductState],
  (product) => product.isLoading
);

export const selectIsError = createSelector(
  [selectProductState],
  (product) => product.isError
);

export const selectError = createSelector(
  [selectProductState],
  (product) => product.error
);

export const selectLoadingState = createSelector(
  [selectIsLoading, selectIsError, selectError, selectIsProductReady],
  (isLoading, isError, error, isReady) => ({
    isLoading,
    isError,
    error,
    isReady,
    hasError: isError && !!error,
  })
);

// ============================================================================
// COMPREHENSIVE SELECTORS
// ============================================================================

export const selectProductData = createSelector(
  [
    selectStrapiProduct,
    selectMedusaProduct,
    selectActiveVariant,
    selectHasProductData,
    selectIsProductReady,
  ],
  (strapiProduct, medusaProduct, activeVariant, hasData, isReady) => ({
    strapiProduct,
    medusaProduct,
    activeVariant,
    hasData,
    isReady,
  })
);

export const selectCompleteProductInfo = createSelector(
  [
    selectProductData,
    selectProductMetadata,
    selectPricingData,
    selectImageData,
    selectLoadingState,
    selectVariantAvailability,
  ],
  (productData, metadata, pricing, images, loading, variantAvailability) => ({
    ...productData,
    ...metadata,
    ...pricing,
    ...images,
    ...loading,
    ...variantAvailability,
  })
);

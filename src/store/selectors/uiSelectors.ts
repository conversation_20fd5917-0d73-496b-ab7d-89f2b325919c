import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "../index";

// ============================================================================
// BASE SELECTORS
// ============================================================================

const selectUIState = (state: RootState) => state.ui;
const selectProductState = (state: RootState) => state.product;

// ============================================================================
// MODAL SELECTORS
// ============================================================================

export const selectIsModalOpen = createSelector(
  [selectUIState],
  (ui) => ui.isModalOpen
);

export const selectSelectedImageIndex = createSelector(
  [selectUIState],
  (ui) => ui.selectedImageIndex
);

export const selectCurrentModalImage = createSelector(
  [selectProductState, selectSelectedImageIndex],
  (product, selectedIndex) =>
    product.combinedImages[selectedIndex] || product.combinedImages[0] || ""
);

export const selectHasModalImage = createSelector(
  [selectCurrentModalImage],
  (currentImage) => !!currentImage
);

export const selectModalState = createSelector(
  [
    selectIsModalOpen,
    selectSelectedImageIndex,
    selectCurrentModalImage,
    selectHasModalImage,
  ],
  (isModalOpen, selectedImageIndex, currentModalImage, hasModalImage) => ({
    isModalOpen,
    selectedImageIndex,
    currentModalImage,
    hasModalImage,
  })
);

// ============================================================================
// CAROUSEL SELECTORS
// ============================================================================

export const selectCurrentSlide = createSelector(
  [selectUIState],
  (ui) => ui.currentSlide
);

export const selectCurrentCarouselImage = createSelector(
  [selectProductState, selectCurrentSlide],
  (product, currentSlide) =>
    product.combinedImages[currentSlide] || product.combinedImages[0] || ""
);

export const selectCarouselNavigation = createSelector(
  [selectCurrentSlide, selectProductState],
  (currentSlide, product) => {
    const totalSlides = product.combinedImages.length;
    return {
      currentSlide,
      totalSlides,
      canGoNext: currentSlide < totalSlides - 1,
      canGoPrevious: currentSlide > 0,
      isFirstSlide: currentSlide === 0,
      isLastSlide: currentSlide === totalSlides - 1,
    };
  }
);

export const selectCarouselState = createSelector(
  [selectCurrentSlide, selectCurrentCarouselImage, selectCarouselNavigation],
  (currentSlide, currentImage, navigation) => ({
    currentSlide,
    currentImage,
    ...navigation,
  })
);

// ============================================================================
// IMAGE NAVIGATION SELECTORS
// ============================================================================

export const selectImageNavigation = createSelector(
  [selectProductState, selectCurrentSlide, selectSelectedImageIndex],
  (product, currentSlide, selectedImageIndex) => {
    const images = product.combinedImages;
    const totalImages = images.length;

    return {
      images,
      totalImages,
      hasImages: totalImages > 0,
      currentSlide,
      selectedImageIndex,
      hasMultipleImages: totalImages > 1,
      canNavigate: totalImages > 1,
    };
  }
);

export const selectModalNavigation = createSelector(
  [selectSelectedImageIndex, selectProductState],
  (selectedImageIndex, product) => {
    const totalImages = product.combinedImages.length;
    return {
      selectedImageIndex,
      totalImages,
      canGoNext: selectedImageIndex < totalImages - 1,
      canGoPrevious: selectedImageIndex > 0,
      isFirstImage: selectedImageIndex === 0,
      isLastImage: selectedImageIndex === totalImages - 1,
    };
  }
);

// ============================================================================
// LOADING AND ERROR SELECTORS
// ============================================================================

export const selectIsUILoading = createSelector(
  [selectUIState],
  (ui) => ui.isUILoading
);

export const selectUIError = createSelector(
  [selectUIState],
  (ui) => ui.uiError
);

export const selectHasUIError = createSelector(
  [selectUIError],
  (error) => !!error
);

export const selectUILoadingState = createSelector(
  [selectIsUILoading, selectUIError, selectHasUIError],
  (isUILoading, uiError, hasUIError) => ({
    isUILoading,
    uiError,
    hasUIError,
    isReady: !isUILoading && !hasUIError,
  })
);

// ============================================================================
// SYNCHRONIZATION SELECTORS
// ============================================================================

export const selectIsSynchronized = createSelector(
  [selectCurrentSlide, selectSelectedImageIndex],
  (currentSlide, selectedImageIndex) => currentSlide === selectedImageIndex
);

export const selectSynchronizationState = createSelector(
  [selectCurrentSlide, selectSelectedImageIndex, selectIsSynchronized],
  (currentSlide, selectedImageIndex, isSynchronized) => ({
    currentSlide,
    selectedImageIndex,
    isSynchronized,
    needsSync: !isSynchronized,
  })
);

// ============================================================================
// RESPONSIVE UI SELECTORS
// ============================================================================

export const selectResponsiveUIState = createSelector(
  [selectUIState, selectProductState],
  (ui, product) => {
    // This could be enhanced with actual breakpoint detection
    // For now, we'll provide the structure based on available data
    const hasImages = product.combinedImages.length > 0;
    const hasMultipleImages = product.combinedImages.length > 1;

    return {
      isMobile: false, // Could use a breakpoint hook here
      isTablet: false,
      isDesktop: true,
      showMobileCarousel: false, // Based on screen size
      showDesktopCarousel: true,
      showThumbnails: hasMultipleImages,
      showNavigation: hasMultipleImages,
      showModal: ui.isModalOpen && hasImages,
    };
  }
);

// ============================================================================
// KEYBOARD NAVIGATION SELECTORS
// ============================================================================

export const selectKeyboardNavigationState = createSelector(
  [selectUIState, selectProductState, selectIsModalOpen],
  (ui, product, isModalOpen) => {
    const totalItems = product.combinedImages.length;
    const currentIndex = isModalOpen ? ui.selectedImageIndex : ui.currentSlide;

    return {
      isModalOpen,
      totalItems,
      currentIndex,
      canNavigate: totalItems > 1,
      isFirstItem: currentIndex === 0,
      isLastItem: currentIndex === totalItems - 1,
    };
  }
);

// ============================================================================
// ACCESSIBILITY SELECTORS
// ============================================================================

export const selectAccessibilityState = createSelector(
  [selectUIState, selectProductState, selectImageNavigation],
  (ui, product, imageNav) => {
    const currentImageIndex = ui.isModalOpen
      ? ui.selectedImageIndex
      : ui.currentSlide;
    const currentImage = product.combinedImages[currentImageIndex];

    return {
      currentImageIndex: currentImageIndex + 1, // 1-based for screen readers
      totalImages: imageNav.totalImages,
      currentImageAlt: `Product image ${currentImageIndex + 1} of ${imageNav.totalImages}`,
      modalAriaLabel: ui.isModalOpen
        ? "Product image gallery modal"
        : undefined,
      carouselAriaLabel: "Product image carousel",
      hasKeyboardSupport: imageNav.canNavigate,
    };
  }
);

// ============================================================================
// PERFORMANCE SELECTORS
// ============================================================================

export const selectUIPerformanceState = createSelector(
  [selectUIState, selectProductState],
  (ui, product) => {
    const imageCount = product.combinedImages.length;
    const shouldPreloadImages = imageCount <= 10; // Preload only if reasonable number
    const shouldLazyLoad = imageCount > 5;

    return {
      shouldPreloadImages,
      shouldLazyLoad,
      imageCount,
      isLightweightMode: imageCount > 20,
      shouldUseVirtualization: imageCount > 50,
    };
  }
);

// ============================================================================
// COMPREHENSIVE UI SELECTORS
// ============================================================================

export const selectCompleteUIState = createSelector(
  [
    selectModalState,
    selectCarouselState,
    selectImageNavigation,
    selectUILoadingState,
    selectSynchronizationState,
    selectResponsiveUIState,
    selectAccessibilityState,
  ],
  (modal, carousel, imageNav, loading, sync, responsive, accessibility) => ({
    modal,
    carousel,
    imageNavigation: imageNav,
    loading,
    synchronization: sync,
    responsive,
    accessibility,
  })
);

export const selectUIForComponent = createSelector(
  [
    selectCompleteUIState,
    selectKeyboardNavigationState,
    selectUIPerformanceState,
  ],
  (completeUI, keyboard, performance) => ({
    ...completeUI,
    keyboard,
    performance,
  })
);

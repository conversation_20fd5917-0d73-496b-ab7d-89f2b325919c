import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "../index";

// ============================================================================
// BASE SELECTORS
// ============================================================================

const selectThemeState = (state: RootState) => state.theme;

// ============================================================================
// BASIC THEME SELECTORS
// ============================================================================

export const selectPrimaryColor = createSelector(
  [selectThemeState],
  (theme) => theme.primaryColor
);

export const selectBackgroundColor = createSelector(
  [selectThemeState],
  (theme) => theme.backgroundColor
);

export const selectCSSVariables = createSelector(
  [selectThemeState],
  (theme) => theme.cssVariables
);

// ============================================================================
// COMPUTED THEME SELECTORS
// ============================================================================

export const selectThemeStyles = createSelector(
  [selectPrimaryColor, selectBackgroundColor, selectCSSVariables],
  (primaryColor, backgroundColor, cssVariables) => ({
    primaryColor,
    backgroundColor,
    cssVariables: {
      "--product-primary-color": primaryColor,
      "--product-background-color": backgroundColor,
      ...cssVariables,
    } as React.CSSProperties,
  })
);

export const selectInlineStyles = createSelector(
  [selectPrimaryColor, selectBackgroundColor],
  (primaryColor, backgroundColor) =>
    ({
      backgroundColor,
      color: getContrastingTextColor(backgroundColor),
      "--product-primary-color": primaryColor,
      "--product-background-color": backgroundColor,
    }) as React.CSSProperties
);

// ============================================================================
// COLOR UTILITY SELECTORS
// ============================================================================

export const selectPrimaryColorVariations = createSelector(
  [selectPrimaryColor],
  (primaryColor) => ({
    primary: primaryColor,
    primaryRgb: hexToRgb(primaryColor),
    primaryAlpha10: `${primaryColor}1a`,
    primaryAlpha20: `${primaryColor}33`,
    primaryAlpha30: `${primaryColor}4d`,
    primaryAlpha50: `${primaryColor}80`,
    primaryAlpha70: `${primaryColor}b3`,
    primaryAlpha90: `${primaryColor}e6`,
  })
);

export const selectBackgroundColorVariations = createSelector(
  [selectBackgroundColor],
  (backgroundColor) => ({
    background: backgroundColor,
    backgroundRgb: hexToRgb(backgroundColor),
    backgroundAlpha10: `${backgroundColor}1a`,
    backgroundAlpha20: `${backgroundColor}33`,
    backgroundAlpha30: `${backgroundColor}4d`,
    backgroundAlpha50: `${backgroundColor}80`,
    backgroundAlpha70: `${backgroundColor}b3`,
    backgroundAlpha90: `${backgroundColor}e6`,
  })
);

export const selectContrastColors = createSelector(
  [selectPrimaryColor, selectBackgroundColor],
  (primaryColor, backgroundColor) => ({
    primaryContrast: getContrastingTextColor(primaryColor),
    backgroundContrast: getContrastingTextColor(backgroundColor),
    primaryOnBackground: getContrastRatio(primaryColor, backgroundColor) > 4.5,
    backgroundOnPrimary: getContrastRatio(backgroundColor, primaryColor) > 4.5,
  })
);

// ============================================================================
// THEME VALIDATION SELECTORS
// ============================================================================

export const selectThemeValidation = createSelector(
  [selectPrimaryColor, selectBackgroundColor, selectContrastColors],
  (primaryColor, backgroundColor, contrastColors) => ({
    isPrimaryColorValid: isValidHexColor(primaryColor),
    isBackgroundColorValid: isValidHexColor(backgroundColor),
    hasGoodContrast: contrastColors.primaryOnBackground,
    isAccessible:
      contrastColors.primaryOnBackground && contrastColors.backgroundOnPrimary,
    contrastRatio: getContrastRatio(primaryColor, backgroundColor),
  })
);

// ============================================================================
// THEME PRESETS SELECTORS
// ============================================================================

export const selectCurrentThemePreset = createSelector(
  [selectPrimaryColor, selectBackgroundColor],
  (primaryColor, backgroundColor) => {
    const presets = {
      default: { primaryColor: "#036A38", backgroundColor: "#ffffff" },
      dark: { primaryColor: "#4ade80", backgroundColor: "#1f2937" },
      light: { primaryColor: "#059669", backgroundColor: "#f9fafb" },
      green: { primaryColor: "#16a34a", backgroundColor: "#f0fdf4" },
      blue: { primaryColor: "#2563eb", backgroundColor: "#eff6ff" },
    };

    for (const [name, preset] of Object.entries(presets)) {
      if (
        preset.primaryColor === primaryColor &&
        preset.backgroundColor === backgroundColor
      ) {
        return name;
      }
    }

    return "custom";
  }
);

export const selectAvailableThemePresets = createSelector(
  [selectCurrentThemePreset],
  (currentPreset) => [
    {
      name: "default",
      label: "Default Green",
      active: currentPreset === "default",
    },
    { name: "dark", label: "Dark Mode", active: currentPreset === "dark" },
    { name: "light", label: "Light Mode", active: currentPreset === "light" },
    { name: "green", label: "Nature Green", active: currentPreset === "green" },
    { name: "blue", label: "Ocean Blue", active: currentPreset === "blue" },
    { name: "custom", label: "Custom", active: currentPreset === "custom" },
  ]
);

// ============================================================================
// COMPONENT-SPECIFIC THEME SELECTORS
// ============================================================================

export const selectButtonTheme = createSelector(
  [selectPrimaryColor, selectBackgroundColor, selectContrastColors],
  (primaryColor, backgroundColor, contrastColors) => ({
    primary: {
      backgroundColor: primaryColor,
      color: contrastColors.primaryContrast,
      borderColor: primaryColor,
    },
    secondary: {
      backgroundColor: "transparent",
      color: primaryColor,
      borderColor: primaryColor,
    },
    outline: {
      backgroundColor: "transparent",
      color: primaryColor,
      borderColor: primaryColor,
    },
  })
);

export const selectCardTheme = createSelector(
  [selectBackgroundColor, selectPrimaryColor, selectContrastColors],
  (backgroundColor, primaryColor, contrastColors) => ({
    backgroundColor,
    borderColor: `${primaryColor}20`,
    color: contrastColors.backgroundContrast,
    headerColor: primaryColor,
  })
);

export const selectModalTheme = createSelector(
  [selectBackgroundColor, selectPrimaryColor, selectContrastColors],
  (backgroundColor, primaryColor, contrastColors) => ({
    overlayColor: `${backgroundColor}80`,
    backgroundColor,
    color: contrastColors.backgroundContrast,
    borderColor: primaryColor,
    closeButtonColor: contrastColors.backgroundContrast,
  })
);

// ============================================================================
// COMPREHENSIVE THEME SELECTORS
// ============================================================================

export const selectCompleteTheme = createSelector(
  [
    selectThemeStyles,
    selectPrimaryColorVariations,
    selectBackgroundColorVariations,
    selectContrastColors,
    selectThemeValidation,
    selectCurrentThemePreset,
  ],
  (
    styles,
    primaryVariations,
    backgroundVariations,
    contrastColors,
    validation,
    currentPreset
  ) => ({
    ...styles,
    primaryVariations,
    backgroundVariations,
    contrastColors,
    validation,
    currentPreset,
  })
);

export const selectThemeForComponents = createSelector(
  [selectCompleteTheme, selectButtonTheme, selectCardTheme, selectModalTheme],
  (completeTheme, buttonTheme, cardTheme, modalTheme) => ({
    ...completeTheme,
    components: {
      button: buttonTheme,
      card: cardTheme,
      modal: modalTheme,
    },
  })
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Convert hex color to RGB values
 */
function hexToRgb(hex: string): string {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return "0, 0, 0";

  return [
    parseInt(result[1], 16),
    parseInt(result[2], 16),
    parseInt(result[3], 16),
  ].join(", ");
}

/**
 * Validate hex color format
 */
function isValidHexColor(color: string): boolean {
  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
}

/**
 * Get contrasting text color for a given background color
 */
function getContrastingTextColor(backgroundColor: string): string {
  // Remove # if present
  const hex = backgroundColor.replace("#", "");

  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return black for light backgrounds, white for dark backgrounds
  return luminance > 0.5 ? "#000000" : "#ffffff";
}

/**
 * Calculate contrast ratio between two colors
 */
function getContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string) => {
    const hex = color.replace("#", "");
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    const toLinear = (c: number) =>
      c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);

    return 0.2126 * toLinear(r) + 0.7152 * toLinear(g) + 0.0722 * toLinear(b);
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);

  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
}

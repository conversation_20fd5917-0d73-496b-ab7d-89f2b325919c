{"kind": "collectionType", "collectionName": "templates", "info": {"singularName": "template", "pluralName": "templates", "displayName": "Template", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string", "required": true}, "page_type": {"type": "enumeration", "enum": ["PRODUCT", "CATEGORY"], "required": true}, "blocks": {"type": "dynamiczone", "components": ["pdp-templates.ingredient-details-template", "pdp-templates.whats-inside-template", "pdp-templates.review-template", "pdp-templates.product-details-extra-template", "pdp-templates.product-additional-description-template", "pdp-templates.nutritional-facts-template", "pdp-templates.got-a-question-template", "pdp-templates.certificate-banner-template", "review-templates.real-people-reviews-template", "pdp-templates.why-section-template", "pdp-templates.quickly-added-template", "review-templates.verified-reviews-template", "pdp-templates.key-features-template", "faqs.faq-template", "banner.single-banner", "banner.banner-carousel", "refer.refer-and-earn"]}, "products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product", "mappedBy": "template"}}}
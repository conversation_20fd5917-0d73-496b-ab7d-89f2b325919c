// Interface automatically generated by schemas-to-ts

import { Product } from '../../../product/content-types/product/product';
import { Product_Plain } from '../../../product/content-types/product/product';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export enum PageTypeEnum {
  Product = 'PRODUCT',
  Category = 'CATEGORY',}

export interface Template {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title: string;
  page_type: PageTypeEnum;
  blocks?: any;
  products: { data: Product[] };
}
export interface Template_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title: string;
  page_type: PageTypeEnum;
  blocks?: any;
  products: Product_Plain[];
}

export interface Template_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title: string;
  page_type: PageTypeEnum;
  blocks?: any;
  products: number[];
}

export interface Template_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title: string;
  page_type: PageTypeEnum;
  blocks?: any;
  products: AdminPanelRelationPropertyModification<Product_Plain>;
}

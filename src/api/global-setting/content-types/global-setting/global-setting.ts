// Interface automatically generated by schemas-to-ts

import { CertificateManager } from '../../../../components/certificates/interfaces/CertificateManager';
import { RealPeopleReviews } from '../../../../components/reviews/interfaces/RealPeopleReviews';
import { Cashback } from '../../../../components/common/interfaces/Cashback';
import { AnnouncementBar } from '../../../../components/common/interfaces/AnnouncementBar';
import { CertificateManager_Plain } from '../../../../components/certificates/interfaces/CertificateManager';
import { RealPeopleReviews_Plain } from '../../../../components/reviews/interfaces/RealPeopleReviews';
import { Cashback_Plain } from '../../../../components/common/interfaces/Cashback';
import { AnnouncementBar_Plain } from '../../../../components/common/interfaces/AnnouncementBar';
import { CertificateManager_NoRelations } from '../../../../components/certificates/interfaces/CertificateManager';
import { RealPeopleReviews_NoRelations } from '../../../../components/reviews/interfaces/RealPeopleReviews';
import { Cashback_NoRelations } from '../../../../components/common/interfaces/Cashback';
import { AnnouncementBar_NoRelations } from '../../../../components/common/interfaces/AnnouncementBar';

export interface GlobalSetting {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  certificates?: CertificateManager;
  real_people_reviews?: RealPeopleReviews;
  tax_description?: string;
  cashback?: Cashback;
  announcement_bar?: AnnouncementBar;
}
export interface GlobalSetting_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  certificates?: CertificateManager_Plain;
  real_people_reviews?: RealPeopleReviews_Plain;
  tax_description?: string;
  cashback?: Cashback_Plain;
  announcement_bar?: AnnouncementBar_Plain;
}

export interface GlobalSetting_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  certificates?: CertificateManager_NoRelations;
  real_people_reviews?: RealPeopleReviews_NoRelations;
  tax_description?: string;
  cashback?: Cashback_NoRelations;
  announcement_bar?: AnnouncementBar_NoRelations;
}

export interface GlobalSetting_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  certificates?: CertificateManager_Plain;
  real_people_reviews?: RealPeopleReviews_Plain;
  tax_description?: string;
  cashback?: Cashback_Plain;
  announcement_bar?: AnnouncementBar_Plain;
}

// Interface automatically generated by schemas-to-ts

export enum PageTypeEnum {
  Page = 'PAGE',
  Index = 'INDEX',}

export interface Page {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
}
export interface Page_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
}

export interface Page_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
}

export interface Page_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
}

{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "Homepage + Static Pages", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"handle": {"type": "string", "unique": true, "required": true}, "page_type": {"type": "enumeration", "enum": ["PAGE", "INDEX"], "required": true}, "blocks": {"type": "dynamiczone", "components": ["banner.single-banner", "banner.banner-carousel", "common.marquee", "elements.html-editor", "contact-us.contact-us-page", "elements.header", "media.external-media-embed", "refer.refer-and-earn", "career.employee-photo-grid", "reviews.truth-sayers", "reviews.stop-the-press", "reviews.startup-fam", "reviews.our-community", "reviews.love-wall", "reviews.love-for-content", "reviews.join-the-gang", "reviews.influencer-truth", "reviews.celebrity-stars", "media.image", "elements.title", "career.youtube-banner", "career.whole-gang", "career.values", "career.stop-the-lies", "career.people-living-values", "career.job-positions", "career.hiring-process"]}}}
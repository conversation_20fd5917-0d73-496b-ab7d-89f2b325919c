// Interface automatically generated by schemas-to-ts

import { Product } from '../../../product/content-types/product/product';
import { Product_Plain } from '../../../product/content-types/product/product';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export interface Category {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
  cross_selling_products?: { data: Product[] };
  bg_color?: any;
}
export interface Category_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
  cross_selling_products?: Product_Plain[];
  bg_color?: any;
}

export interface Category_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
  cross_selling_products?: number[];
  bg_color?: any;
}

export interface Category_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
  cross_selling_products?: AdminPanelRelationPropertyModification<Product_Plain>;
  bg_color?: any;
}

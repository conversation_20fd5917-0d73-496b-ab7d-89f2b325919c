{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "systemId": {"type": "string", "unique": true, "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "handle": {"type": "string"}, "productType": {"type": "string"}, "variants": {"type": "relation", "relation": "oneToMany", "target": "api::product-variant.product-variant", "mappedBy": "product"}, "primary_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color", "required": true}, "bg_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "required": true, "customField": "plugin::color-picker.color"}, "whats_inside": {"type": "component", "repeatable": false, "component": "pdp.whats-inside"}, "nutritional_facts": {"type": "component", "repeatable": false, "component": "pdp.nutritional-facts"}, "template": {"type": "relation", "relation": "manyToOne", "target": "api::template.template", "inversedBy": "products"}, "show_certificates": {"type": "boolean", "default": false, "required": false}, "show_real_people_reviews": {"type": "boolean", "default": false}, "why_section": {"type": "component", "repeatable": false, "component": "pdp.why-section"}, "faqs": {"type": "component", "repeatable": false, "component": "faqs.faqs"}, "quickly_added_category": {"type": "relation", "relation": "oneToOne", "target": "api::category.category"}, "show_navigation_chips": {"type": "boolean", "default": false}, "key_features": {"type": "component", "repeatable": false, "component": "pdp.key-features"}, "show_review_navigation": {"type": "boolean", "default": true, "required": false}, "short_description": {"type": "text"}, "product_detail_extra": {"type": "component", "repeatable": false, "component": "pdp.product-details-extra"}, "additional_description": {"type": "component", "repeatable": false, "component": "pdp.product-additional-description"}, "show_delievery_option": {"type": "boolean", "default": true, "required": true}, "offer": {"displayName": "Offer", "type": "component", "repeatable": false, "component": "pdp.offer"}, "banner_images": {"type": "component", "repeatable": true, "component": "banner.single-banner"}, "single_product_title": {"type": "text"}, "cross_selling_categories": {"type": "relation", "relation": "manyToMany", "target": "api::category.category", "inversedBy": "cross_selling_products"}}}
import { sdk } from "@/libs/medusaClient";
import { getStrapiProductDetails } from "@/libs/strapiApis";
import { FetchResult } from "@/types/Common";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";

interface FetchProductDataParams {
  productHandle?: string;
}

export async function fetchProductData({
  productHandle,
}: FetchProductDataParams): Promise<FetchResult> {
  try {
    const strapiProduct = await getStrapiProductDetails({ productHandle });

    if (!strapiProduct) {
      return { success: false, error: "Product not found in Strapi" };
    }

    const typedStrapiProduct = strapiProduct as unknown as ProductDetailsType;

    // Get the Medusa product ID from Strapi product
    const medusaProductId = typedStrapiProduct.systemId as string;

    if (!medusaProductId) {
      return {
        success: false,
        error: "Product ID not found for Medusa",
      };
    }

    // Fetch the Medusa product using the ID from Strapi
    const medusaProduct = await sdk.store.product.list({
      id: medusaProductId,
      fields:
        "+variants.calculated_price.*,+variants.extended_product_variants.*,cms_product.*,+variants.prices.*,+variants.variant_image.*",
      region_id: "reg_01JWBD587RMPB20Y9ARTGJMT2D",
    });

    if (!medusaProduct || !medusaProduct.products?.length) {
      return {
        success: false,
        error: "Product not found in Medusa",
      };
    }

    return {
      success: true,
      data: {
        strapiProduct: typedStrapiProduct,
        medusaProduct: medusaProduct.products[0],
      },
    };
  } catch (error) {
    console.error("Error in fetchProductData:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? `Failed to fetch product data: ${error.message}`
          : "An unknown error occurred while fetching product data",
    };
  }
}

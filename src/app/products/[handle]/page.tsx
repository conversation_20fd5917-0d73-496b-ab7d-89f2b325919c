import React from "react";
import { getProductDetails } from "@/libs/middlewareAPIs";
import { HydratedProductPage } from "@/components/Partials/PDP/HydratedProductPage";
import { PageTypeEnum } from "@/types/Page";
import { notFound } from "next/navigation";

const ProductPage = async ({
  params,
}: {
  params: Promise<{ handle: string }>;
}) => {
  const { handle } = await params;

  const productData = await getProductDetails({
    productHandle: handle,
  });

  // Handle missing product data at the page level
  if (!productData?.strapiProduct && !productData?.medusaProduct) {
    notFound();
  }

  const medusaProductData = productData?.medusaProduct;

  return (
    <HydratedProductPage
      page={handle}
      pageType={PageTypeEnum.Product}
      productData={productData?.strapiProduct}
      medusaProductData={medusaProductData}
    />
  );
};

export default ProductPage;

// Enable static generation for better performance
export async function generateStaticParams() {
  // You can implement this to pre-generate popular product pages
  // For now, we'll use dynamic generation with ISR
  return [];
}

// Enable ISR (Incremental Static Regeneration) for instant loading
export const revalidate = 3600; // Revalidate every hour

import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Footer from "@/components/Common/Footer";
import Header from "@/components/blocks/Header";
import { AnnouncementBar } from "@/components/Common/AnnouncementBar";
import FooterNav from "@/components/Common/FooterNav";
import { Toaster } from "@/components/ui/sonner";
import { QueryProvider } from "@/providers/query-client-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="stylesheet" href="https://use.typekit.net/jyz6scz.css" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${poppins.variable} antialiased`}
      >
        <QueryProvider>
          <Header />
          <AnnouncementBar
            display={true}
            variant="secondary"
            messages={[
              { text: "15% Off over ₹5000 Use Code CLEANLABEL" },
              {
                text: "NEW Pista Badaam Protein – Use Code THANDAI Get 15% off",
              },
              { text: "Orders dispatch between 24-48 hours" },
            ]}
          />
          <main className="w-full max-w-[100vw] overflow-x-hidden">
            {children}
          </main>
          <Footer />
          <FooterNav />
          <Toaster />
        </QueryProvider>
      </body>
    </html>
  );
}

"use client";
import SeactionHeading from "@/components/Common/Section/Heading";
import TwoBannerSection from "@/components/Common/TwoBannerSection";
import ReviewGrid from "@/components/blocks/ReviewGrid";
import { customerReviews } from "@/data/reviews";
import Image from "next/image";
import React from "react";
import { Product } from "@/components/Common/ProductCard";
import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";
import { Love } from "@/assets/icons/Love";
import SingleBanner from "@/components/Common/SingleBanner";
import TryBanner from "@/components/Common/TryBanner";
import FlipableCardsSection from "@/components/Sections/FlipableCardsSection";
import ProductCarouselSection from "@/components/Sections/ProductCarouselSection";
import MarqueeSection from "@/components/Sections/MarqueeSection";
import Link from "next/link";
import Compare from "@/components/blocks/Compare";
import DetailedIngredientsDetails from "@/components/blocks/DetailedIngredientsDetails";

const Page = () => {
  const cards = [
    {
      id: 1,
      frontImage: "/images/reviews/review_card_1.png",
      backImage: "/images/reviews/review_card_1.png",
      frontAlt: "Card 1 Front",
      backAlt: "Card 1 Back",
    },
    {
      id: 2,
      frontImage: "/images/reviews/review_card_2.png",
      backImage: "/images/reviews/review_card_2.png",
      frontAlt: "Card 2 Front",
      backAlt: "Card 2 Back",
    },
    {
      id: 3,
      frontImage: "/images/reviews/review_card_3.png",
      backImage: "/images/reviews/review_card_3.png",
      frontAlt: "Card 3 Front",
      backAlt: "Card 3 Back",
    },
    {
      id: 4,
      frontImage: "/images/reviews/review_card_4.png",
      backImage: "/images/reviews/review_card_4.png",
      frontAlt: "Card 4 Front",
      backAlt: "Card 4 Back",
    },
    {
      id: 5,
      frontImage: "/images/reviews/review_card_1.png",
      backImage: "/images/reviews/review_card_1.png",
      frontAlt: "Card 5 Front",
      backAlt: "Card 5 Back",
    },
    {
      id: 6,
      frontImage: "/images/reviews/review_card_2.png",
      backImage: "/images/reviews/review_card_2.png",
      frontAlt: "Card 6 Front",
      backAlt: "Card 6 Back",
    },
    {
      id: 7,
      frontImage: "/images/reviews/review_card_3.png",
      backImage: "/images/reviews/review_card_3.png",
      frontAlt: "Card 7 Front",
      backAlt: "Card 7 Back",
    },
  ];

  const reviewCards = [
    {
      id: 1,
      frontImage: "/images/reviews/protein_powder_pro/1.png",
      backImage: "/images/reviews/protein_powder_pro/1_back.png",
      frontAlt: "Card 1 Front",
      backAlt: "Card 1 Back",
    },
    {
      id: 2,
      frontImage: "/images/reviews/protein_powder_pro/2.png",
      backImage: "/images/reviews/protein_powder_pro/2.png",
      frontAlt: "Card 2 Front",
      backAlt: "Card 2 Back",
    },
    {
      id: 3,
      frontImage: "/images/reviews/protein_powder_pro/3.png",
      backImage: "/images/reviews/protein_powder_pro/3_back.png",
      frontAlt: "Card 3 Front",
      backAlt: "Card 3 Back",
    },
    {
      id: 4,
      frontImage: "/images/reviews/protein_powder_pro/4.png",
      backImage: "/images/reviews/protein_powder_pro/4.png",
      frontAlt: "Card 4 Front",
      backAlt: "Card 4 Back",
    },
    {
      id: 5,
      frontImage: "/images/reviews/protein_powder_pro/5.png",
      backImage: "/images/reviews/protein_powder_pro/5_back.png",
      frontAlt: "Card 5 Front",
      backAlt: "Card 5 Back",
    },
    {
      id: 6,
      frontImage: "/images/reviews/protein_powder_pro/6.png",
      backImage: "/images/reviews/protein_powder_pro/6.png",
      frontAlt: "Card 6 Front",
      backAlt: "Card 6 Back",
    },
  ];

  const accentCardProducts: Product[] = [
    {
      id: "1",
      title: "Mango Milkshake 24g Protein Powder - Pack of 1 KG",
      image: "/images/products/double_cocoa.png",
      price: 4777,
      originalPrice: 4499, // 1% discount
      isStartingPrice: false,
      rating: 2.5,
      weight: "8 x 27 g",
      primaryColor: "#00693B", // matches double cocoa
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      description:
        "Delicious mango-flavored protein powder with 24g protein per serving. Perfect for post-workout recovery.",
      variants: [
        {
          id: "1-1",
          size: "Pack of 1",
          weight: "250g",
          price: 4777,
          originalPrice: 4499, // ~6% discount
          image: "/images/products/protein_fop.png",
        },
        {
          id: "1-2",
          size: "500g",
          weight: "500g",
          price: 2799,
          originalPrice: 2999, // ~7% discount
          image: "/images/products/double_cocoa.png",
        },
      ],
    },
    {
      id: "2",
      title: "Everyone Party - Pack of 16 Mini Protein Bars",
      image: "/images/products/protein_2.webp",
      price: 960,
      isStartingPrice: false,
      rating: 4.1,
      weight: "16 x 27 g",
      primaryColor: "#44426a",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      description:
        "Delicious mango-flavored protein powder with 24g protein per serving. Perfect for post-workout recovery.",
    },
    {
      id: "3",
      title: "Personalised Box - Pack of 24 Mini Protein Bars",
      image: "/images/products/protein_3.png",
      price: 1440,
      isStartingPrice: true,
      rating: 4.7,
      weight: "24 x 27 g",
      primaryColor: "#DC8A20",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      description:
        "Delicious mango-flavored protein powder with 24g protein per serving. Perfect for post-workout recovery.",
    },
    {
      id: "4",
      title: "Personalised Box - Pack of 48 Mini Protein Bars",
      image: "/images/products/double_cocoa.png",
      price: 2880,
      isStartingPrice: true,
      rating: 4.2,
      weight: "48 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      description:
        "Delicious mango-flavored protein powder with 24g protein per serving. Perfect for post-workout recovery.",
    },
    {
      id: "5",
      title: "Double Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/double_cocoa.png",
      price: 720,
      isStartingPrice: false,
      rating: 4.5,
      weight: "12 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      description:
        "Delicious mango-flavored protein powder with 24g protein per serving. Perfect for post-workout recovery.",
    },
    {
      id: "6",
      title: "Cranberry Mini Protein Bars - Box of 12",
      image: "/images/products/double_cocoa.png",
      price: 720,
      originalPrice: 800, // 10% discount
      isStartingPrice: false,
      rating: 2.4,
      weight: "12 x 27 g",
      primaryColor: "#D23C47", // red tone
      enableProductBg: true, // Enable background color
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      description:
        "Delicious mango-flavored protein powder with 24g protein per serving. Perfect for post-workout recovery.",
      variants: [
        {
          id: "6-1",
          size: "1 x 350g",
          weight: "350g",
          price: 720,
          originalPrice: 800, // 10% discount
          image: "/images/products/double_cocoa.png",
        },
        {
          id: "6-2",
          size: "2 x 350g",
          weight: "700g",
          price: 1350,
          originalPrice: 1500, // 10% discount
          image: "/images/products/double_cocoa.png",
        },
        {
          id: "6-3",
          size: "3 x 350g",
          weight: "1050g",
          price: 1950,
          originalPrice: 2200, // ~11% discount
          image: "/images/products/double_cocoa.png",
        },
      ],
    },
    {
      id: "7",
      title: "Peanut Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/double_cocoa.png",
      price: 720,
      isStartingPrice: false,
      rating: 5,
      weight: "12 x 27 g",
      primaryColor: "#F05C1D", // orange tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      description:
        "Delicious mango-flavored protein powder with 24g protein per serving. Perfect for post-workout recovery.",
    },
    {
      id: "8",
      title: "Coffee Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/double_cocoa.png",
      price: 720,
      isStartingPrice: false,
      rating: 5,
      weight: "12 x 27 g",
      primaryColor: "#59382B", // brown tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      description:
        "Delicious mango-flavored protein powder with 24g protein per serving. Perfect for post-workout recovery.",
    },
  ];

  const bannerImage = {
    id: "16",
    mweb_image: {
      alternativeText: null,
      url: "/images/heroes/mango_mobile.png",
      mime: "image/png",
    },
    web_image: {
      alternativeText: null,
      url: "/images/heroes/mango_desktop.jpg",
      mime: "image/jpeg",
    },
  };

  // TODO: will add when fix image ratio issue
  // const transperncyImages = {
  //   id: "16",
  //   mweb_image: {
  //     alternativeText: null,
  //     url: "/images/banners/transperncy_mobile.png",
  //     mime: "image/png",
  //   },
  //   web_image: {
  //     alternativeText: null,
  //     url: "/images/banners/transparency.webp",
  //     mime: "image/jpeg",
  //   },
  // };

  const tryBannerImage = {
    id: "16",
    mweb_image: {
      alternativeText: null,
      url: "/images/banners/protein_getting_started_mobile.webp",
      mime: "image/webp",
    },
    web_image: {
      alternativeText: null,
      url: "/images/banners/protein_getting_started.webp",
      mime: "image/webp",
    },
  };

  return (
    <>

      <SingleBanner image={bannerImage} />

      <FlipableCardsSection
        title="Why choose us?"
        titleColor="#1a181e"
        backgroundColor="#F9DEA5"
        cards={cards.slice(0, 5)}
        gapClassName="gap-2"
        scrollbarColor="#EFA146"
      />

      <ProductCarouselSection
        title=" 24g-30g Protein Range"
        products={accentCardProducts}
        backgroundColor="#EFA146"
        titleColor="#FFFFFF"
        hideScrollbar={false}
        enableProductBg={true}
        className="relative"
      />

      <ProductCarouselSection
        title=" 15g Protein Range"
        products={accentCardProducts}
        backgroundColor="#EFA146"
        titleColor="#FFFFFF"
        hideScrollbar={false}
        enableProductBg={true}
      />

      <MarqueeSection
        backgroundColor="#00693B"
        textColor="#FFFFFF"
        content={Array(8).fill("BACK IN STOCK")}
        numberOfCopies={3}
        fade={true}
      />

      <div className="w-full relative">
        <Link href={"/products/protein-bars"}>
          <div className="aspect-[18/5] relative hidden lg:block">
            <Image
              src="/images/banners/protein_try.png"
              alt="landing page desktop"
              fill
              style={{ objectFit: "cover" }}
              priority
            />
          </div>
          <div className="aspect-[1/1] relative block lg:hidden">
            <Image
              src="/images/banners/protein_try_mobile.png"
              alt="landing page mobile"
              fill
              style={{ objectFit: "fill" }}
              priority
            />
          </div>
        </Link>
      </div>

      <MarqueeSection
        backgroundColor="#00693B"
        textColor="#FFFFFF"
        content={Array(8).fill("BACK IN STOCK")}
        numberOfCopies={3}
        fade={true}
        reverse={true}
      />

      <FlipableCardsSection
        title="Real People. Real Reviews."
        titleColor="#1a181e"
        backgroundColor="#efd8e0"
        backgroundImage="/images/banners/protein_review_banner.png"
        cards={reviewCards.slice(0, 6)}
        icon={<Love />}
        iconColor="#E33F52"
        iconClassName="w-9 h-9.5"
        gapClassName="gap-4"
        hideScrollbar={false}
        scrollbarColor="#EFA146"
        cardClassName="h-[500px] w-[250px]"
      />

      <Compare
        title="Us vs Them"
        backgroundImage="/images/banners/test.png"
        titleColor="white"
      />

      {/* //TODO: Need to Build */}
      {/* <SingleBanner image={transperncyImages} /> */}

      {/* Detailed Ingredients Details Section */}
      <DetailedIngredientsDetails />
      {/* Two Banner Section - Full Width */}
      <TwoBannerSection
        banners={[
          {
            id: "1",
            image: "/images/banners/protein_banner_1.png",
            alt: "Protein Powder Banner",
            href: "/categories/protein-powder",
          },
          {
            id: "2",
            image: "/images/banners/protein_banner_2.png",
            alt: "Protein Bars Banner",
            href: "/categories/protein-bars",
          },
          {
            id: "3",
            image: "/images/banners/protein_banner_2.png",
            alt: "Protein Bars Banner",
            href: "/categories/protein-bars",
          },
        ]}
      />

      <div className="bg-[#F9DEA5] py-8">
        <div className="container mx-auto">
          <SeactionHeading
            title="Real People. Real Reviews."
            color="#1a181e"
            icon={<Love />}
            iconColor="#EFA146"
            iconClassName="w-9 h-9.5"
          />
          <div className="mt-7.5">
            <ReviewGrid
              reviews={customerReviews}
              useCarousel={true}
              hideScrollbar={false}
            />
          </div>
        </div>
      </div>

      <TryBanner image={tryBannerImage} href="/products/protein-bars" />

      <PaymentMethodsBar
        paymentMethods={[
          { id: "paytm", name: "Paytm", imageSrc: "/images/payment/paytm.png" },
          { id: "visa", name: "Visa", imageSrc: "/images/payment/visa.png" },
          {
            id: "mastercard",
            name: "Mastercard",
            imageSrc: "/images/payment/mastercard.png",
          },
          {
            id: "maestro",
            name: "Maestro",
            imageSrc: "/images/payment/cred.png",
          },
          {
            id: "american-express",
            name: "American Express",
            imageSrc: "/images/payment/gpay.png",
          },
          {
            id: "phonepe",
            name: "PhonePe",
            imageSrc: "/images/payment/phonepe.png",
          },
        ]}
      />
    </>
  );
};

export default Page;

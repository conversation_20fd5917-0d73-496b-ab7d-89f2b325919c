import { CollectionType } from "@/types/Collections/Collection";
import dynamic from "next/dynamic";
import React from "react";
import { PageTypeEnum } from "@/types/Page";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import ProductView from "../Partials/PDP/ProductView";
import { ExtendedMedusaProductWithStrapiProduct } from "@/types/Medusa/Product";

interface ComponentMapType {
  [key: string]: {
    FF: React.ComponentType<any>;
    Lazy: React.ComponentType<any>;
  };
}

const PDP = {
  ComponentPdpTemplatesQuicklyAddedTemplate: {
    FF: dynamic(() => import("@/components/Partials/PDP/QuickAddCard")),
    Lazy: dynamic(() => import("@/components/Partials/PDP/QuickAddCard")),
  },
  ComponentPdpTemplatesKeyFeaturesTemplate: {
    FF: dynamic(() => import("@/components/Partials/PDP/KeyFeatures")),
    Lazy: dynamic(() => import("@/components/Partials/PDP/KeyFeatures")),
  },
  ComponentReviewTemplatesVerifiedReviewsTemplate: {
    FF: dynamic(() => import("@/components/Partials/PDP/VerifiedReviews")),
    Lazy: dynamic(() => import("@/components/Partials/PDP/VerifiedReviews")),
  },
  ComponentPdpTemplatesGotAQuestionTemplate: {
    FF: dynamic(() => import("@/components/Partials/PDP/GotAQuestion")),
    Lazy: dynamic(() => import("@/components/Partials/PDP/GotAQuestion")),
  },
  ComponentReviewTemplatesRealPeopleReviewsTemplate: {
    FF: dynamic(() => import("@/components/Partials/PDP/RealReviews")),
    Lazy: dynamic(() => import("@/components/Partials/PDP/RealReviews")),
  },
  ComponentPdpTemplatesCertificateBannerTemplate: {
    FF: dynamic(() => import("@/components/Partials/PDP/Certificates")),
    Lazy: dynamic(() => import("@/components/Partials/PDP/Certificates")),
  },
  ComponentPdpTemplatesWhySectionTemplate: {
    FF: dynamic(() => import("@/components/Partials/PDP/WhySection")),
    Lazy: dynamic(() => import("@/components/Partials/PDP/WhySection")),
  },
};

// Combine all components
export const componentMap: ComponentMapType = {
  ...PDP,
};

interface DynamicTemplateProps {
  page: string;
  pageType: PageTypeEnum;
  collectionData?: CollectionType;
  productData?: ProductDetailsType;
  medusaProductData?: ExtendedMedusaProductWithStrapiProduct; // More flexible type to handle different Medusa response formats
}

const DynamicTemplate: React.FC<DynamicTemplateProps> = async ({
  page,
  pageType,
  collectionData,
  productData,
  medusaProductData,
}) => {
  const blocks = productData?.template?.blocks || [];
  if (blocks.length === 0) {
    console.warn(`-> No blocks found for template: ${page}`);
    return <p>page not founds</p>;
  }

  const renderBlock = (block: any, index: number) => {
    const ComponentInfo = componentMap[block.__typename];
    if (!ComponentInfo) return null;

    const Component = ComponentInfo.FF;
    const commonProps = {
      block,
      page,
      pageType,
      collectionData,
      productData,
      medusaProductData,
      priority: index < 4,
    };

    return <Component key={`block-${index}`} {...(commonProps as any)} />;
  };

  const blockElements = blocks.map((block: any, index: number) =>
    renderBlock(block, index)
  );

  return (
    <div style={{ backgroundColor: productData?.bg_color }}>
      <ProductView
        productDataOk={productData}
        medusaProductData={medusaProductData}
      />
      {blockElements}
    </div>
  );
};

export default DynamicTemplate;

// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface SingleBanner {
  web?: { data: Media };
  mobile?: { data: Media };
  action_link?: string;
}
export interface SingleBanner_Plain {
  web?: Media_Plain;
  mobile?: Media_Plain;
  action_link?: string;
}

export interface SingleBanner_NoRelations {
  web?: number;
  mobile?: number;
  action_link?: string;
}


// Interface automatically generated by schemas-to-ts

import { Image } from '../../media/interfaces/Image';
import { Media } from '../../../common/schemas-to-ts/Media';
import { Image_Plain } from '../../media/interfaces/Image';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { Image_NoRelations } from '../../media/interfaces/Image';

export enum ShowOnEnum {
  Web = 'WEB',
  Mobile = 'MOBILE',
  Both = 'BOTH',}

export interface BannerWithTitle {
  title?: string;
  subtitle?: string;
  title_color?: any;
  subtitle_color?: any;
  image?: Image;
  bg_color?: any;
  show_on?: ShowOnEnum;
  bg_image?: { data: Media };
}
export interface BannerWithTitle_Plain {
  title?: string;
  subtitle?: string;
  title_color?: any;
  subtitle_color?: any;
  image?: Image_Plain;
  bg_color?: any;
  show_on?: ShowOnEnum;
  bg_image?: Media_Plain;
}

export interface BannerWithTitle_NoRelations {
  title?: string;
  subtitle?: string;
  title_color?: any;
  subtitle_color?: any;
  image?: Image_NoRelations;
  bg_color?: any;
  show_on?: ShowOnEnum;
  bg_image?: number;
}


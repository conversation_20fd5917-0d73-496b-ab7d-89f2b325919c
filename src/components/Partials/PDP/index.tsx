// Export the new refactored component as default
export { default } from "./ProductDetailsPage";

// Export the new component with explicit name
export { ProductDetailsPage } from "./ProductDetailsPage";

// Export context and hooks for external use
export { ProductProvider, useProduct } from "./context/ProductContext";

// Export all hooks
export * from "./hooks";

// Export all types
export * from "./types";

// Export all components
export * from "./components";

// Legacy export for backward compatibility
import { ProductDetailsPage } from "./ProductDetailsPage";
export { ProductDetailsPage as ProductDetails };

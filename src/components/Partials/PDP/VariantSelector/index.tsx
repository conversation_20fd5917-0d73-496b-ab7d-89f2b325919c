"use client";
import React from "react";
import { cn } from "@/libs/utils";
import { useProductTheme } from "../hooks/ui";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { setActiveVariant } from "@/store/slices/productSlice";

const VariantSelector: React.FC = () => {
  const { primaryColor } = useProductTheme();
  const dispatch = useAppDispatch();
  const variants = useAppSelector(
    (state) => state.product.medusaProduct?.variants || []
  );
  const activeVariant = useAppSelector((state) => state.product.activeVariant);

  const handleVariantChange = (variant: any) => {
    dispatch(setActiveVariant(variant));
  };

  return (
    <div className="mb-3 flex items-center gap-4">
      {variants.map((variant) => (
        <div key={variant.id} className="relative flex-1">
          <button
            onClick={() => handleVariantChange(variant)}
            className={cn(
              "uppercase h-[35px] w-full rounded-[8px] text-[11.5px] font-obviously leading-[1.5px]",
              activeVariant?.id === variant.id
                ? "text-white font-semibold"
                : "border-[1.5px]"
            )}
            style={
              activeVariant?.id === variant.id
                ? { backgroundColor: primaryColor }
                : { borderColor: primaryColor, color: primaryColor }
            }
          >
            {variant.title}
          </button>

          {/* Show best deal badge conditionally — you can change the logic here */}
          {true && (
            <span
              className="absolute -top-2.5 -left-2.5 z-10 w-[60px] h-[20px] bg-[url('https://dms.mydukaan.io/original/webp/media/5046cf71-d0aa-47ab-aa4a-a84aa818490c.png')] bg-no-repeat bg-contain bg-center"
              aria-hidden="true"
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default VariantSelector;

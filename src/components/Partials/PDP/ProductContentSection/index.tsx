"use client";

import React from "react";
import ProductCarouselSection from "@/components/Partials/PDP/ProductCarouselSection";
import ProductRatingSection from "@/components/Partials/PDP/ProductRatingSection";
import ProductShortDescription from "@/components/Partials/PDP/ProductShortDescription";
import WhatsInsideSection from "@/components/Partials/PDP/WhatsInsideSection";
import NutritionalFactsAccordion from "@/components/Partials/PDP/NutritionalFacts";
import ProductDetailsAccordion from "@/components/Partials/PDP/ProductDetailsAccordion";
import ProductAdditionalDescription from "@/components/Partials/PDP/ProductAdditionalDescription";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { useProductTheme } from "../ProductView";

/**
 * ProductContentSection Component
 *
 * Contains all the right-side content of the product page including
 * carousel, ratings, descriptions, and accordion sections.
 *
 * @param productData - Complete product data
 * @param images - Array of product images
 * @param title - Product title
 * @param currentSlide - Current carousel slide index
 * @param onSlideChange - Carousel slide change handler
 */
interface ProductContentSectionProps {
  productData?: ProductDetailsType;
  images: string[];
  title: string;
  currentSlide: number;
  onSlideChange: (index: number) => void;
}

const ProductContentSection: React.FC<ProductContentSectionProps> = ({
  productData,
  images,
  title,
  currentSlide,
  onSlideChange,
}) => {
  const { primaryColor } = useProductTheme();

  return (
    <div className="col-span-12 lg:col-span-7">
      {/* Product Carousel */}
      <ProductCarouselSection
        images={images}
        title={title}
        currentSlide={currentSlide}
        onSlideChange={onSlideChange}
        primaryColor={primaryColor}
      />

      {/* Rating and Reviews */}
      <ProductRatingSection
        rating={4.0}
        reviewCount={306}
        reviewsHref="#reviews"
      />

      {/* Short Description */}
      <ProductShortDescription description={productData?.short_description} />

      {/* What's Inside Section */}
      <WhatsInsideSection data={productData?.whats_inside} />

      {/* Nutritional Facts */}
      <div className="mt-4">
        <NutritionalFactsAccordion
          data={productData?.nutritional_facts}
          borderColor={primaryColor}
        />
      </div>

      {/* Product Details */}
      <ProductDetailsAccordion
        data={productData?.product_detail_extra}
        borderColor={primaryColor}
      />

      {/* Additional Description */}
      <ProductAdditionalDescription
        data={productData?.additional_description}
      />
    </div>
  );
};

export default ProductContentSection;

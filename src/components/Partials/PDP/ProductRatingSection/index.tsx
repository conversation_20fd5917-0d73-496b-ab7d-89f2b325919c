"use client";

import React from "react";
import Link from "next/link";
import { ReviewStar } from "@/assets/icons/ReviewStar";
import ChevronDown from "@/assets/icons/ChevronDown";
import { getStarFillPercentage } from "@/libs/utils";
import { useProductTheme } from "../hooks/ui";

/**
 * ProductRatingSection Component
 *
 * Displays product rating information including star rating, review count,
 * and navigation to reviews section.
 *
 * @param rating - The product rating (0-5)
 * @param reviewCount - Number of verified reviews
 * @param reviewsHref - Link to reviews section (default: "#reviews")
 */
interface ProductRatingSectionProps {
  rating: number;
  reviewCount: number;
  reviewsHref?: string;
}

const ProductRatingSection: React.FC<ProductRatingSectionProps> = ({
  rating,
  reviewCount,
  reviewsHref = "#reviews",
}) => {
  const { primaryColor } = useProductTheme();

  return (
    <div className="mb-3 flex items-start justify-between">
      {/* Star Rating Display */}
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <ReviewStar
            key={star}
            color="#FFC83A"
            fillPercentage={getStarFillPercentage(rating, star - 1)}
          />
        ))}
      </div>

      {/* Rating Text and Reviews Link */}
      <div className="flex flex-col">
        <p
          style={{ color: primaryColor }}
          className="self-end text-sm font-semibold leading-5 text-black font-obviously"
        >
          {rating.toFixed(1)} out of 5
        </p>
        <Link href={reviewsHref}>
          <div className="flex items-center gap-0.5">
            <span
              style={{ color: primaryColor }}
              className="text-sm font-normal text-black font-obviously"
            >
              {reviewCount} verified reviews
            </span>
            <ChevronDown className="h-5 w-5" color={primaryColor} />
          </div>
        </Link>
      </div>
    </div>
  );
};

export default ProductRatingSection;

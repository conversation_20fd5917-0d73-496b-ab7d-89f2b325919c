"use client";
import React from "react";
import { useProductTheme } from "../hooks/ui";
import { useProductCart, useCartButtonState } from "@/store/hooks";
import { AddToCartData } from "../types";

/**
 * AddToCartButton Component
 *
 * Displays the add to cart button with loading state support.
 * Uses Redux for state management and ProductTheme for consistent theming.
 *
 * @param onAddToCart - Optional external callback function when button is clicked
 */
interface AddToCartButtonProps {
  onAddToCart?: (data: AddToCartData) => void;
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({ onAddToCart }) => {
  const { primaryColor } = useProductTheme();
  const { addToCart } = useProductCart();
  const { buttonText, buttonDisabled } = useCartButtonState();

  const handleAddToCart = async () => {
    await addToCart();

    // Call external callback if provided
    if (onAddToCart) {
      // You can pass the cart data here if needed
      onAddToCart({} as AddToCartData);
    }
  };
  return (
    <div className="mb-4">
      <button
        className="h-12.5 uppercase w-full p-2 rounded-[6px] text-white text-center font-semibold text-sm font-obviously border"
        style={{
          backgroundColor: primaryColor,
          borderColor: primaryColor,
        }}
        onClick={handleAddToCart}
        disabled={buttonDisabled}
      >
        {buttonText}
      </button>
    </div>
  );
};

export default AddToCartButton;

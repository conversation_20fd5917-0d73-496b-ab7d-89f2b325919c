"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  useMemo,
} from "react";
import {
  ProductContextValue,
  ProductProviderProps,
  AddToCartData,
} from "../types";
import { productReducer, initialProductState } from "./productReducer";
import { useProductImages } from "../hooks/useProductImages";
import { calculateDiscount } from "@/utils/pricing";

// ============================================================================
// CONTEXT CREATION
// ============================================================================

const ProductContext = createContext<ProductContextValue | null>(null);

// ============================================================================
// CUSTOM HOOK FOR ACCESSING CONTEXT
// ============================================================================

export const useProduct = (): ProductContextValue => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error("useProduct must be used within a ProductProvider");
  }
  return context;
};

// ============================================================================
// PRODUCT PROVIDER COMPONENT
// ============================================================================

export const ProductProvider: React.FC<ProductProviderProps> = ({
  children,
  strapiProduct,
  medusaProduct,
  onAddToCart,
  onCouponClick,
}) => {
  // Initialize state with provided data
  const [state, dispatch] = useReducer(productReducer, {
    ...initialProductState,
    strapiProduct: strapiProduct || null,
    medusaProduct: medusaProduct || null,
    activeVariant: medusaProduct?.variants?.[0] || null,
    primaryColor: strapiProduct?.primary_color || "#036A38",
    backgroundColor: strapiProduct?.bg_color || "#ffffff",
  });

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  // Generate combined images using existing hook
  const combinedImages = useProductImages(
    state.medusaProduct || ({} as any),
    state.activeVariant || ({} as any)
  );

  // Calculate pricing information
  const currentPrice = useMemo(() => {
    if (!state.activeVariant) return 0;
    return (
      state.activeVariant.extended_product_variants?.discounted_price ||
      state.activeVariant.calculated_price?.calculated_amount ||
      0
    );
  }, [state.activeVariant]);

  const originalPrice = useMemo(() => {
    if (!state.activeVariant) return 0;
    return state.activeVariant.calculated_price?.calculated_amount || 0;
  }, [state.activeVariant]);

  const loyaltyPoints = useMemo(() => {
    return Math.floor(currentPrice * 0.02);
  }, [currentPrice]);

  const discountPercentage = useMemo(() => {
    return calculateDiscount(currentPrice, originalPrice);
  }, [currentPrice, originalPrice]);

  // Generate breadcrumb items
  const breadcrumbItems = useMemo(
    () => [
      { label: "Home", href: "/" },
      { label: "Products", href: "/products" },
      { label: state.medusaProduct?.title || "Product" },
    ],
    [state.medusaProduct?.title]
  );

  // ============================================================================
  // ACTION HANDLERS
  // ============================================================================

  const setActiveVariant = useCallback((variant: any) => {
    dispatch({ type: "SET_ACTIVE_VARIANT", payload: variant });
    // Reset slide when variant changes
    dispatch({ type: "SET_CURRENT_SLIDE", payload: 0 });
  }, []);

  const setQuantity = useCallback((quantity: number) => {
    if (quantity >= 1) {
      dispatch({ type: "SET_QUANTITY", payload: quantity });
    }
  }, []);

  const incrementQuantity = useCallback(() => {
    dispatch({ type: "INCREMENT_QUANTITY" });
  }, []);

  const decrementQuantity = useCallback(() => {
    dispatch({ type: "DECREMENT_QUANTITY" });
  }, []);

  const setCurrentSlide = useCallback((slide: number) => {
    dispatch({ type: "SET_CURRENT_SLIDE", payload: slide });
  }, []);

  const openModal = useCallback((imageIndex?: number) => {
    dispatch({ type: "OPEN_MODAL" });
    if (imageIndex !== undefined) {
      dispatch({ type: "SET_SELECTED_IMAGE_INDEX", payload: imageIndex });
    }
  }, []);

  const closeModal = useCallback(() => {
    dispatch({ type: "CLOSE_MODAL" });
  }, []);

  const addToCart = useCallback(
    async (data?: Partial<AddToCartData>) => {
      if (!state.medusaProduct || !state.activeVariant) {
        console.error("Cannot add to cart: missing product or variant data");
        return;
      }

      dispatch({ type: "SET_LOADING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      try {
        const cartData: AddToCartData = {
          product: state.medusaProduct,
          variant: state.activeVariant,
          quantity: state.quantity,
          totalPrice: currentPrice * state.quantity,
          ...data,
        };

        if (onAddToCart) {
          await onAddToCart(cartData);
        }

        console.log("Added to cart:", cartData);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to add to cart";
        dispatch({ type: "SET_ERROR", payload: errorMessage });
        console.error("Add to cart error:", error);
      } finally {
        dispatch({ type: "SET_LOADING", payload: false });
      }
    },
    [
      state.medusaProduct,
      state.activeVariant,
      state.quantity,
      currentPrice,
      onAddToCart,
    ]
  );

  const applyCoupon = useCallback(() => {
    if (onCouponClick) {
      onCouponClick();
    }
  }, [onCouponClick]);

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const contextValue: ProductContextValue = useMemo(
    () => ({
      // Data
      strapiProduct: state.strapiProduct,
      medusaProduct: state.medusaProduct,

      // UI State
      activeVariant: state.activeVariant,
      quantity: state.quantity,
      currentSlide: state.currentSlide,
      isModalOpen: state.isModalOpen,
      selectedImageIndex: state.selectedImageIndex,

      // Theme
      primaryColor: state.primaryColor,
      backgroundColor: state.backgroundColor,

      // Loading & Error
      isLoading: state.isLoading,
      isError: state.isError,
      error: state.error,

      // Computed Values
      combinedImages,
      currentPrice,
      originalPrice,
      loyaltyPoints,
      discountPercentage,
      breadcrumbItems,

      // Actions
      setActiveVariant,
      setQuantity,
      incrementQuantity,
      decrementQuantity,
      setCurrentSlide,
      openModal,
      closeModal,
      addToCart,
      applyCoupon,
    }),
    [
      state,
      combinedImages,
      currentPrice,
      originalPrice,
      loyaltyPoints,
      discountPercentage,
      breadcrumbItems,
      setActiveVariant,
      setQuantity,
      incrementQuantity,
      decrementQuantity,
      setCurrentSlide,
      openModal,
      closeModal,
      addToCart,
      applyCoupon,
    ]
  );

  return (
    <ProductContext.Provider value={contextValue}>
      <div
        className="min-h-screen"
        style={
          {
            backgroundColor: state.backgroundColor,
            "--product-primary-color": state.primaryColor,
          } as React.CSSProperties
        }
      >
        {children}
      </div>
    </ProductContext.Provider>
  );
};

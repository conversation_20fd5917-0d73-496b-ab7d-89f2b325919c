import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { ExtendedMedusaProductWithStrapiProduct, ExtendedVariant } from "@/types/Medusa/Product";

// ============================================================================
// STATE INTERFACE
// ============================================================================

export interface ProductState {
  // Data
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;
  
  // UI State
  activeVariant: ExtendedVariant | null;
  quantity: number;
  currentSlide: number;
  isModalOpen: boolean;
  selectedImageIndex: number;
  
  // Theme
  primaryColor: string;
  backgroundColor: string;
  
  // Loading & Error States
  isLoading: boolean;
  isError: boolean;
  error: string | null;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

export const initialProductState: ProductState = {
  // Data
  strapiProduct: null,
  medusaProduct: null,
  
  // UI State
  activeVariant: null,
  quantity: 1,
  currentSlide: 0,
  isModalOpen: false,
  selectedImageIndex: 0,
  
  // Theme
  primaryColor: "#036A38",
  backgroundColor: "#ffffff",
  
  // Loading & Error States
  isLoading: false,
  isError: false,
  error: null,
};

// ============================================================================
// ACTION TYPES
// ============================================================================

export type ProductAction =
  // Data Actions
  | { type: "SET_STRAPI_PRODUCT"; payload: ProductDetailsType | null }
  | { type: "SET_MEDUSA_PRODUCT"; payload: ExtendedMedusaProductWithStrapiProduct | null }
  
  // UI Actions
  | { type: "SET_ACTIVE_VARIANT"; payload: ExtendedVariant | null }
  | { type: "SET_QUANTITY"; payload: number }
  | { type: "INCREMENT_QUANTITY" }
  | { type: "DECREMENT_QUANTITY" }
  | { type: "SET_CURRENT_SLIDE"; payload: number }
  | { type: "OPEN_MODAL" }
  | { type: "CLOSE_MODAL" }
  | { type: "SET_SELECTED_IMAGE_INDEX"; payload: number }
  
  // Theme Actions
  | { type: "SET_PRIMARY_COLOR"; payload: string }
  | { type: "SET_BACKGROUND_COLOR"; payload: string }
  
  // Loading & Error Actions
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "CLEAR_ERROR" }
  
  // Reset Actions
  | { type: "RESET_UI_STATE" }
  | { type: "RESET_ALL" };

// ============================================================================
// REDUCER FUNCTION
// ============================================================================

export const productReducer = (state: ProductState, action: ProductAction): ProductState => {
  switch (action.type) {
    // ========================================================================
    // DATA ACTIONS
    // ========================================================================
    
    case "SET_STRAPI_PRODUCT":
      return {
        ...state,
        strapiProduct: action.payload,
        primaryColor: action.payload?.primary_color || state.primaryColor,
        backgroundColor: action.payload?.bg_color || state.backgroundColor,
      };
    
    case "SET_MEDUSA_PRODUCT":
      return {
        ...state,
        medusaProduct: action.payload,
        activeVariant: action.payload?.variants?.[0] || null,
        currentSlide: 0, // Reset slide when product changes
      };
    
    // ========================================================================
    // UI ACTIONS
    // ========================================================================
    
    case "SET_ACTIVE_VARIANT":
      return {
        ...state,
        activeVariant: action.payload,
        currentSlide: 0, // Reset slide when variant changes
      };
    
    case "SET_QUANTITY":
      return {
        ...state,
        quantity: Math.max(1, action.payload), // Ensure minimum quantity of 1
      };
    
    case "INCREMENT_QUANTITY":
      return {
        ...state,
        quantity: state.quantity + 1,
      };
    
    case "DECREMENT_QUANTITY":
      return {
        ...state,
        quantity: Math.max(1, state.quantity - 1), // Ensure minimum quantity of 1
      };
    
    case "SET_CURRENT_SLIDE":
      return {
        ...state,
        currentSlide: Math.max(0, action.payload), // Ensure non-negative slide index
      };
    
    case "OPEN_MODAL":
      return {
        ...state,
        isModalOpen: true,
      };
    
    case "CLOSE_MODAL":
      return {
        ...state,
        isModalOpen: false,
      };
    
    case "SET_SELECTED_IMAGE_INDEX":
      return {
        ...state,
        selectedImageIndex: Math.max(0, action.payload), // Ensure non-negative index
      };
    
    // ========================================================================
    // THEME ACTIONS
    // ========================================================================
    
    case "SET_PRIMARY_COLOR":
      return {
        ...state,
        primaryColor: action.payload,
      };
    
    case "SET_BACKGROUND_COLOR":
      return {
        ...state,
        backgroundColor: action.payload,
      };
    
    // ========================================================================
    // LOADING & ERROR ACTIONS
    // ========================================================================
    
    case "SET_LOADING":
      return {
        ...state,
        isLoading: action.payload,
        // Clear error when starting to load
        ...(action.payload && { error: null, isError: false }),
      };
    
    case "SET_ERROR":
      return {
        ...state,
        error: action.payload,
        isError: action.payload !== null,
        isLoading: false, // Stop loading when error occurs
      };
    
    case "CLEAR_ERROR":
      return {
        ...state,
        error: null,
        isError: false,
      };
    
    // ========================================================================
    // RESET ACTIONS
    // ========================================================================
    
    case "RESET_UI_STATE":
      return {
        ...state,
        quantity: 1,
        currentSlide: 0,
        isModalOpen: false,
        selectedImageIndex: 0,
        error: null,
        isError: false,
        isLoading: false,
      };
    
    case "RESET_ALL":
      return {
        ...initialProductState,
        // Preserve data if available
        strapiProduct: state.strapiProduct,
        medusaProduct: state.medusaProduct,
        activeVariant: state.medusaProduct?.variants?.[0] || null,
        primaryColor: state.strapiProduct?.primary_color || initialProductState.primaryColor,
        backgroundColor: state.strapiProduct?.bg_color || initialProductState.backgroundColor,
      };
    
    // ========================================================================
    // DEFAULT CASE
    // ========================================================================
    
    default:
      console.warn(`Unknown action type: ${(action as any).type}`);
      return state;
  }
};

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Creates an action creator for better type safety
 */
export const createProductAction = <T extends ProductAction["type"]>(
  type: T
) => (payload?: Extract<ProductAction, { type: T }>["payload"]) => ({
  type,
  payload,
} as Extract<ProductAction, { type: T }>);

/**
 * Validates the product state for consistency
 */
export const validateProductState = (state: ProductState): boolean => {
  // Check for required data consistency
  if (state.medusaProduct && !state.activeVariant) {
    console.warn("Product has no active variant selected");
    return false;
  }
  
  // Check for valid quantity
  if (state.quantity < 1) {
    console.warn("Invalid quantity: must be at least 1");
    return false;
  }
  
  // Check for valid slide index
  if (state.currentSlide < 0) {
    console.warn("Invalid slide index: must be non-negative");
    return false;
  }
  
  return true;
};

"use client";
import React from "react";
import Minus from "@/assets/icons/Minus";
import Plus from "@/assets/icons/Plus";
import { useProductTheme } from "../hooks/ui";
import { useProductCart } from "@/store/hooks";

/**
 * QuantitySelector Component
 *
 * Displays quantity selection controls with increment/decrement buttons.
 * Uses Redux for state management and ProductTheme for consistent theming.
 */
interface QuantitySelectorProps {
  label?: string;
}

const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  label = "BOX",
}) => {
  const { primaryColor } = useProductTheme();
  const { quantity, incrementQuantity, decrementQuantity } = useProductCart();

  const handleDecrease = () => {
    decrementQuantity();
  };

  const handleIncrease = () => {
    incrementQuantity();
  };

  return (
    <div className="mb-4">
      <div
        className="h-10 rounded-sm overflow-hidden flex items-center justify-between border bg-white"
        style={{ borderColor: primaryColor }}
      >
        <button
          className="h-10 w-10 flex items-center justify-center"
          style={{ backgroundColor: primaryColor }}
          onClick={handleDecrease}
        >
          <Minus />
        </button>
        <label
          className="flex-1 w-full text-center uppercase text-sm font-[550] leading-5 font-obviously"
          style={{ color: primaryColor }}
        >
          {quantity} {label}
        </label>
        <button
          className="h-10 w-10 flex items-center justify-center"
          style={{ backgroundColor: primaryColor }}
          onClick={handleIncrease}
        >
          <Plus />
        </button>
      </div>
    </div>
  );
};

export default QuantitySelector;

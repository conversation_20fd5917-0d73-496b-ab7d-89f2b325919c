"use client";

import React from "react";
import { cn } from "@/libs/utils";
import { ProductContentProps } from "../../types";
import { ProductMedia } from "./ProductMedia";
import { ProductDescription } from "./ProductDescription";
import { ProductDetails } from "./ProductDetails";

/**
 * ProductContent Component
 * 
 * Main product content section containing:
 * - Product media (carousel, images)
 * - Product descriptions and details
 * - Product reviews and additional information
 */
export const ProductContent: React.FC<ProductContentProps> = ({ className }) => {
  return (
    <div className={cn("col-span-12 lg:col-span-7", className)}>
      {/* Product Media */}
      <ProductMedia />

      {/* Product Description */}
      <ProductDescription />

      {/* Product Details */}
      <ProductDetails />
    </div>
  );
};

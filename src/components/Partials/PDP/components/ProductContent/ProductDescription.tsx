"use client";

import React from "react";
import ProductShortDescription from "../../ProductShortDescription";
import WhatsInsideSection from "../../WhatsInsideSection";
import ProductAdditionalDescription from "../../ProductAdditionalDescription";
import { useAppSelector } from "@/store/hooks";

/**
 * ProductDescription Component
 *
 * Contains all product description sections:
 * - Short description
 * - What's inside section
 * - Additional description
 */
export const ProductDescription: React.FC = () => {
  const strapiProduct = useAppSelector((state) => state.product.strapiProduct);

  return (
    <>
      {/* Short Description */}
      <ProductShortDescription description={strapiProduct?.short_description} />

      {/* What's Inside Section */}
      <WhatsInsideSection data={strapiProduct?.whats_inside} />

      {/* Additional Description */}
      <ProductAdditionalDescription
        data={strapiProduct?.additional_description}
      />
    </>
  );
};

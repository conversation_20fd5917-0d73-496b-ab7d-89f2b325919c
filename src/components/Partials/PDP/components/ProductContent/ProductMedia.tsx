"use client";

import React from "react";
import ProductCarouselSection from "../../ProductCarouselSection";
import ProductRatingSection from "../../ProductRatingSection";
import { useProductData } from "@/store/hooks";
import { useProductUI, useProductTheme } from "../../hooks/ui";

/**
 * ProductMedia Component
 *
 * Contains product carousel and rating section
 */
export const ProductMedia: React.FC = () => {
  const { medusaProduct } = useProductData();
  const { images, currentSlide, setCurrentSlide } = useProductUI();
  const { primaryColor } = useProductTheme();

  return (
    <>
      {/* Product Carousel */}
      <ProductCarouselSection
        images={images}
        title={medusaProduct?.title || "Product"}
        currentSlide={currentSlide}
        onSlideChange={setCurrentSlide}
        primaryColor={primaryColor}
      />

      {/* Rating and Reviews */}
      <ProductRatingSection
        rating={4.0}
        reviewCount={306}
        reviewsHref="#reviews"
      />
    </>
  );
};

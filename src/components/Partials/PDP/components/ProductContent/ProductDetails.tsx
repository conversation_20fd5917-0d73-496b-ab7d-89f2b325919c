"use client";

import React from "react";
import NutritionalFactsAccordion from "../../NutritionalFacts";
import ProductDetailsAccordion from "../../ProductDetailsAccordion";
import { useProductData } from "@/store/hooks";
import { useProductTheme } from "../../hooks/ui";

/**
 * ProductDetails Component
 *
 * Contains product detail accordions:
 * - Nutritional facts
 * - Product details
 */
export const ProductDetails: React.FC = () => {
  const { strapiProduct } = useProductData();
  const { primaryColor } = useProductTheme();

  return (
    <>
      {/* Nutritional Facts */}
      <div className="mt-4">
        <NutritionalFactsAccordion
          data={strapiProduct?.nutritional_facts}
          borderColor={primaryColor}
        />
      </div>

      {/* Product Details */}
      <ProductDetailsAccordion
        data={strapiProduct?.product_detail_extra}
        borderColor={primaryColor}
      />
    </>
  );
};

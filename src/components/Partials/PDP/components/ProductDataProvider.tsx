"use client";

import React, { useEffect, useRef } from "react";
import { useAppDispatch } from "@/store/hooks";
import { setStrapiProduct, setMedusaProduct, setActiveVariant } from "@/store/slices/productSlice";
import { initializeThemeFromProduct } from "@/store/slices/themeSlice";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { ExtendedMedusaProductWithStrapiProduct } from "@/types/Medusa/Product";

interface ProductDataProviderProps {
  children: React.ReactNode;
  strapiProduct?: ProductDetailsType | null;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct | null;
}

/**
 * ProductDataProvider Component
 * 
 * Optimized provider that synchronously initializes Redux store with server-side data
 * - No async operations or loading states
 * - Immediate data availability
 * - Single synchronous initialization
 */
export const ProductDataProvider: React.FC<ProductDataProviderProps> = ({
  children,
  strapiProduct,
  medusaProduct,
}) => {
  const dispatch = useAppDispatch();
  const initializedRef = useRef(false);

  // Synchronous initialization - runs only once
  useEffect(() => {
    if (initializedRef.current) return;
    
    // Synchronously set all data at once
    if (strapiProduct) {
      dispatch(setStrapiProduct(strapiProduct));
      
      // Initialize theme immediately
      if (strapiProduct.primary_color || strapiProduct.bg_color) {
        dispatch(initializeThemeFromProduct({
          primaryColor: strapiProduct.primary_color,
          backgroundColor: strapiProduct.bg_color,
        }));
      }
    }

    if (medusaProduct) {
      dispatch(setMedusaProduct(medusaProduct));
      
      // Set first variant as active immediately
      if (medusaProduct.variants?.[0]) {
        dispatch(setActiveVariant(medusaProduct.variants[0]));
      }
    }

    initializedRef.current = true;
  }, []); // Empty dependency array - run only once

  return <>{children}</>;
};

"use client";

import React from "react";
import VariantSelector from "../../VariantSelector";
import QuantitySelector from "../../QuantitySelector";
import AddToCartButton from "../../AddToCartButton";
import BestPriceCoupon from "../../BestPriceCoupon";
import DeliveryInfo from "../../DeliveryInfoCard";
// import { useProductData, useProductCart } from "@/store/hooks";
// import { useAppSelector } from "@/store/hooks";
import { AddToCartData } from "../../types";

/**
 * ProductActions Component
 *
 * Contains all product interaction elements:
 * - Variant selection
 * - Quantity selection
 * - Add to cart button
 * - Best price coupon
 * - Delivery information
 */
interface ProductActionsProps {
  onAddToCart?: (data: AddToCartData) => void;
  onCouponClick?: () => void;
}

export const ProductActions: React.FC<ProductActionsProps> = ({
  onAddToCart,
  onCouponClick,
}) => {
  // Note: These are available for future use if needed
  // const { strapiProduct } = useProductData();
  // const { quantity, setQuantity, addToCart } = useProductCart();
  // const activeVariant = useAppSelector((state) => state.product.activeVariant);
  // const variants = useAppSelector((state) => state.product.medusaProduct?.variants || []);
  // const currentPrice = useAppSelector((state) => state.product.currentPrice);

  const handleCouponClick = () => {
    if (onCouponClick) {
      onCouponClick();
    } else {
      console.log("Coupon clicked");
    }
  };

  return (
    <>
      <div className="flex flex-col">
        {/* Variant Selector */}
        <VariantSelector />

        {/* Quantity Selector */}
        <QuantitySelector />

        {/* Add to Cart Button */}
        <AddToCartButton onAddToCart={onAddToCart} />
      </div>

      {/* Best Price Coupon */}
      <BestPriceCoupon onCouponClick={handleCouponClick} />

      {/* Delivery Information */}
      <DeliveryInfo />
    </>
  );
};

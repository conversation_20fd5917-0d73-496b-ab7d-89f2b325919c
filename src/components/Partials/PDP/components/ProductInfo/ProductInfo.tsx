"use client";

import React from "react";
import { cn } from "@/libs/utils";
import { ProductInfoProps } from "../../types";
import { ProductHeader } from "./ProductHeader";
import { ProductPricing } from "./ProductPricing";
import { ProductActions } from "./ProductActions";
import { ProductMobileCarousel } from "./ProductMobileCarousel";

/**
 * ProductInfo Component
 *
 * Main product information section containing:
 * - Product header (breadcrumb, title, delivery info)
 * - Mobile product carousel
 * - Product pricing
 * - Product actions (variants, quantity, cart)
 */
export const ProductInfo: React.FC<ProductInfoProps> = ({
  className,
  onAddToCart,
  onCouponClick,
}) => {
  return (
    <div className={cn("col-span-12 lg:col-span-5 lg:pr-12", className)}>
      {/* Product Header */}
      <ProductHeader />

      {/* Mobile Product Carousel */}
      <ProductMobileCarousel />

      {/* Product Pricing */}
      <ProductPricing />

      {/* Product Actions */}
      <ProductActions onAddToCart={onAddToCart} onCouponClick={onCouponClick} />
    </div>
  );
};

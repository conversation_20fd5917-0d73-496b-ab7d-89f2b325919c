"use client";

import React from "react";
import MobileProductCarousel from "../../MobileProductCarousel";
import { useAppSelector } from "@/store/hooks";
import { useProductUI, useProductTheme } from "../../hooks/ui";

/**
 * ProductMobileCarousel Component
 *
 * Wrapper for the mobile product carousel with Redux integration
 */
export const ProductMobileCarousel: React.FC = () => {
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);
  const { images, currentSlide, setCurrentSlide } = useProductUI();
  const { primaryColor } = useProductTheme();

  return (
    <MobileProductCarousel
      images={images}
      title={medusaProduct?.title || "Product"}
      currentSlide={currentSlide}
      onSlideChange={setCurrentSlide}
      primaryColor={primaryColor}
    />
  );
};

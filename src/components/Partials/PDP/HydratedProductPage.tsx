"use client";

import React, { useEffect, useRef } from 'react';
import { useAppDispatch } from '@/store/hooks';
import { setStrapiProduct, setMedusaProduct, setActiveVariant } from '@/store/slices/productSlice';
import { initializeThemeFromProduct } from '@/store/slices/themeSlice';
import { ProductDetailsType } from '@/types/Collections/ProductDetails';
import { ExtendedMedusaProductWithStrapiProduct } from '@/types/Medusa/Product';
import DynamicTemplate from '@/components/DynamicTemplate';
import { PageTypeEnum } from '@/types/Page';

// ============================================================================
// HYDRATED PRODUCT PAGE COMPONENT
// ============================================================================

interface HydratedProductPageProps {
  page: string;
  pageType: PageTypeEnum;
  productData?: ProductDetailsType | null;
  medusaProductData?: ExtendedMedusaProductWithStrapiProduct | null;
}

/**
 * HydratedProductPage Component
 * 
 * This component immediately hydrates the Redux store with product data
 * before rendering the page, ensuring instant loading without useEffect delays.
 * 
 * The hydration happens synchronously in the component initialization,
 * not in useEffect, to prevent any loading states.
 */
export const HydratedProductPage: React.FC<HydratedProductPageProps> = ({
  page,
  pageType,
  productData,
  medusaProductData,
}) => {
  const dispatch = useAppDispatch();
  const hydratedRef = useRef(false);

  // Immediate synchronous hydration - no useEffect delay
  if (!hydratedRef.current && (productData || medusaProductData)) {
    // Hydrate Strapi product data
    if (productData) {
      dispatch(setStrapiProduct(productData));
      
      // Initialize theme immediately
      if (productData.primary_color || productData.bg_color) {
        dispatch(initializeThemeFromProduct({
          primaryColor: productData.primary_color,
          backgroundColor: productData.bg_color,
        }));
      }
    }

    // Hydrate Medusa product data
    if (medusaProductData) {
      dispatch(setMedusaProduct(medusaProductData));
      
      // Set first variant as active immediately
      if (medusaProductData.variants?.[0]) {
        dispatch(setActiveVariant(medusaProductData.variants[0]));
      }
    }

    hydratedRef.current = true;
  }

  // Render the page immediately with hydrated data
  return (
    <DynamicTemplate
      page={page}
      pageType={pageType}
      productData={productData}
      medusaProductData={medusaProductData}
    />
  );
};

export default HydratedProductPage;

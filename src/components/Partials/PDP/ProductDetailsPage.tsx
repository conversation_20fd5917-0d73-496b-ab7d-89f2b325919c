"use client";

import React from "react";
import { ProductLayout } from "./components/ProductLayout";
import { ProductInfo } from "./components/ProductInfo";
import { ProductContent } from "./components/ProductContent";
import { ProductDetailsPageProps } from "./types";

/**
 * ProductDetailsPage Component
 *
 * Main product details page component with Redux architecture:
 * - Uses Redux for state management (pre-hydrated)
 * - Eliminates prop drilling
 * - Provides clean component structure
 * - Instant loading with no useEffect delays
 * - Data is already available in Redux store when component renders
 */
export const ProductDetailsPage: React.FC<ProductDetailsPageProps> = ({
  onAddToCart,
  onCouponClick,
}) => {
  // No initialization needed - data is already in Redux store
  // from server-side hydration via HydratedProductPage

  return (
    <ProductLayout>
      {/* Product Information Section */}
      <ProductInfo onAddToCart={onAddToCart} onCouponClick={onCouponClick} />

      {/* Product Content Section */}
      <ProductContent />
    </ProductLayout>
  );
};

// Export as default for backward compatibility
export default ProductDetailsPage;

"use client";

import React, { useEffect } from "react";
import { ProductLayout } from "./components/ProductLayout";
import { ProductInfo } from "./components/ProductInfo";
import { ProductContent } from "./components/ProductContent";
import { ProductDetailsPageProps } from "./types";
import { useProductDataActions, useThemeActions } from "@/store/hooks";

/**
 * ProductDetailsPage Component
 *
 * Main product details page component with Redux architecture:
 * - Uses Redux for state management
 * - Eliminates prop drilling
 * - Provides clean component structure
 * - Maintains identical UI and functionality
 */
export const ProductDetailsPage: React.FC<ProductDetailsPageProps> = ({
  strapiProduct,
  medusaProduct,
  onAddToCart,
  onCouponClick,
}) => {
  const { initializeProduct } = useProductDataActions();
  const { initializeFromProduct } = useThemeActions();

  // Initialize Redux store with product data
  useEffect(() => {
    if (strapiProduct || medusaProduct) {
      initializeProduct({
        strapiProduct,
        medusaProduct,
      });

      // Initialize theme from Strapi product data
      if (strapiProduct?.primary_color || strapiProduct?.bg_color) {
        initializeFromProduct({
          primaryColor: strapiProduct.primary_color,
          backgroundColor: strapiProduct.bg_color,
        });
      }
    }
  }, [strapiProduct, medusaProduct, initializeProduct, initializeFromProduct]);

  return (
    <ProductLayout>
      {/* Product Information Section */}
      <ProductInfo onAddToCart={onAddToCart} onCouponClick={onCouponClick} />

      {/* Product Content Section */}
      <ProductContent />
    </ProductLayout>
  );
};

// Export as default for backward compatibility
export default ProductDetailsPage;

// Data management hooks
export {
  useProductData,
  useStrapiProduct,
  useMedusaProduct,
  useProductAvailability,
  useProductMetadata,
} from "./useProductData";
export {
  useProductPricing,
  useTotalPrice,
  useLoyaltyInfo,
  usePriceComparison,
} from "./useProductPricing";
export {
  useProductVariants,
  useVariantInfo,
  useVariantSelection,
  useVariantAvailability,
} from "./useProductVariants";
export {
  useProductCart,
  useQuantityConstraints,
  useCartButtonState,
  useCartMessages,
} from "./useProductCart";

// Optimized hooks for instant loading
export {
  useOptimizedProductData,
  useOptimizedStrapiProduct,
  useOptimizedMedusaProduct,
  useOptimizedProductAvailability,
  useOptimizedProductMetadata,
} from "./useOptimizedProductData";

// Re-export the existing image hook for consistency
export { useProductImages } from "../useProductImages";

// Export types
export type * from "../../types";

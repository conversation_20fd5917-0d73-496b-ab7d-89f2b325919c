import { useMemo } from "react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { setActiveVariant } from "@/store/slices/productSlice";
import { UseProductVariantsReturn } from "../../types";
import { ExtendedVariant } from "@/types/Medusa/Product";

/**
 * Hook for managing product variants
 * Provides variant selection and information
 */
export const useProductVariants = (): UseProductVariantsReturn => {
  const dispatch = useAppDispatch();
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);
  const activeVariant = useAppSelector((state) => state.product.activeVariant);

  // Get all available variants
  const variants = useMemo(() => {
    return medusaProduct?.variants || [];
  }, [medusaProduct?.variants]);

  // Check if product has variants
  const hasVariants = useMemo(() => {
    return variants.length > 1;
  }, [variants.length]);

  // Variant selection handler
  const handleSetActiveVariant = useMemo(() => {
    return (variant: ExtendedVariant) => {
      dispatch(setActiveVariant(variant));
    };
  }, [dispatch]);

  return useMemo(
    (): UseProductVariantsReturn => ({
      variants,
      activeVariant,
      setActiveVariant: handleSetActiveVariant,
      hasVariants,
    }),
    [variants, activeVariant, handleSetActiveVariant, hasVariants]
  );
};

/**
 * Hook for variant-specific information
 */
export const useVariantInfo = (variant?: ExtendedVariant) => {
  const activeVariant = useAppSelector((state) => state.product.activeVariant);
  const targetVariant = variant || activeVariant;

  return useMemo(() => {
    if (!targetVariant) {
      return {
        title: "",
        sku: "",
        barcode: "",
        isAvailable: false,
        inventory: 0,
        price: 0,
        discountedPrice: 0,
        hasDiscount: false,
      };
    }

    const price = targetVariant.calculated_price?.calculated_amount || 0;
    const discountedPrice =
      targetVariant.extended_product_variants?.discounted_price || price;
    const hasDiscount = discountedPrice < price;

    return {
      title: targetVariant.title || "",
      sku: targetVariant.sku || "",
      barcode: targetVariant.barcode || "",
      isAvailable: targetVariant.inventory_quantity > 0,
      inventory: targetVariant.inventory_quantity || 0,
      price,
      discountedPrice,
      hasDiscount,
    };
  }, [targetVariant]);
};

/**
 * Hook for variant selection logic
 */
export const useVariantSelection = () => {
  const { variants, activeVariant, setActiveVariant } = useProductVariants();

  const selectVariantById = useMemo(
    () => (variantId: string) => {
      const variant = variants.find((v) => v.id === variantId);
      if (variant) {
        setActiveVariant(variant);
      }
    },
    [variants, setActiveVariant]
  );

  const selectVariantByIndex = useMemo(
    () => (index: number) => {
      const variant = variants[index];
      if (variant) {
        setActiveVariant(variant);
      }
    },
    [variants, setActiveVariant]
  );

  const getVariantIndex = useMemo(() => {
    if (!activeVariant) return -1;
    return variants.findIndex((v) => v.id === activeVariant.id);
  }, [variants, activeVariant]);

  const canSelectNext = useMemo(() => {
    const currentIndex = getVariantIndex;
    return currentIndex >= 0 && currentIndex < variants.length - 1;
  }, [getVariantIndex, variants.length]);

  const canSelectPrevious = useMemo(() => {
    const currentIndex = getVariantIndex;
    return currentIndex > 0;
  }, [getVariantIndex]);

  const selectNext = useMemo(
    () => () => {
      if (canSelectNext) {
        selectVariantByIndex(getVariantIndex + 1);
      }
    },
    [canSelectNext, selectVariantByIndex, getVariantIndex]
  );

  const selectPrevious = useMemo(
    () => () => {
      if (canSelectPrevious) {
        selectVariantByIndex(getVariantIndex - 1);
      }
    },
    [canSelectPrevious, selectVariantByIndex, getVariantIndex]
  );

  return {
    selectVariantById,
    selectVariantByIndex,
    getVariantIndex,
    canSelectNext,
    canSelectPrevious,
    selectNext,
    selectPrevious,
  };
};

/**
 * Hook for variant availability checking
 */
export const useVariantAvailability = () => {
  const { variants } = useProductVariants();

  return useMemo(() => {
    const availableVariants = variants.filter((v) => v.inventory_quantity > 0);
    const outOfStockVariants = variants.filter(
      (v) => v.inventory_quantity <= 0
    );

    return {
      availableVariants,
      outOfStockVariants,
      hasAvailableVariants: availableVariants.length > 0,
      allOutOfStock: availableVariants.length === 0 && variants.length > 0,
      availabilityMap: variants.reduce((acc, variant) => {
        acc[variant.id] = variant.inventory_quantity > 0;
        return acc;
      }, {} as Record<string, boolean>),
    };
  }, [variants]);
};

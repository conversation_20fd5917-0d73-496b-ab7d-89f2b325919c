import { useMemo } from "react";
import { useProductData as useReduxProductData } from "@/store/hooks";
import { UseProductDataReturn } from "../../types";

/**
 * Hook for accessing combined product data from both Strapi and Medusa
 * Provides a unified interface for product information
 * Now uses Redux instead of Context API
 */
export const useProductData = (): UseProductDataReturn => {
  const { strapiProduct, medusaProduct, isLoading, isError, error } =
    useReduxProductData();

  // Memoize the return value to prevent unnecessary re-renders
  const result = useMemo(
    (): UseProductDataReturn => ({
      strapiProduct,
      medusaProduct,
      isLoading,
      isError,
      error,
    }),
    [strapiProduct, medusaProduct, isLoading, isError, error]
  );

  return result;
};

/**
 * Hook for accessing only Strapi product data
 */
export const useStrapiProduct = () => {
  const { strapiProduct } = useReduxProductData();
  return strapiProduct;
};

/**
 * Hook for accessing only Medusa product data
 */
export const useMedusaProduct = () => {
  const { medusaProduct } = useReduxProductData();
  return medusaProduct;
};

/**
 * Hook for checking if product data is available
 */
export const useProductAvailability = () => {
  const { strapiProduct, medusaProduct, isLoading, isError } =
    useReduxProductData();

  return useMemo(
    () => ({
      hasData: !!(strapiProduct || medusaProduct),
      hasStrapiData: !!strapiProduct,
      hasMedusaData: !!medusaProduct,
      hasCompleteData: !!(strapiProduct && medusaProduct),
      isReady: !isLoading && !isError && !!(strapiProduct || medusaProduct),
    }),
    [strapiProduct, medusaProduct, isLoading, isError]
  );
};

/**
 * Hook for accessing product metadata
 */
export const useProductMetadata = () => {
  const { strapiProduct, medusaProduct } = useReduxProductData();

  return useMemo(
    () => ({
      title: medusaProduct?.title || strapiProduct?.title || "Product",
      handle: medusaProduct?.handle || "",
      description:
        medusaProduct?.description || strapiProduct?.short_description || "",
      shortDescription: strapiProduct?.short_description || "",
      additionalDescription: strapiProduct?.additional_description || "",
      tags: medusaProduct?.tags || [],
      categories: medusaProduct?.categories || [],
      collection: medusaProduct?.collection || null,
    }),
    [strapiProduct, medusaProduct]
  );
};

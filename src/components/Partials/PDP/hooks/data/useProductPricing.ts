import { useMemo } from "react";
import { useAppSelector } from "@/store/hooks";
import { UseProductPricingReturn } from "../../types";

/**
 * Hook for accessing product pricing information
 * Calculates and formats pricing data based on the active variant
 */
export const useProductPricing = (): UseProductPricingReturn => {
  const currentPrice = useAppSelector((state) => state.product.currentPrice);
  const originalPrice = useAppSelector((state) => state.product.originalPrice);
  const loyaltyPoints = useAppSelector((state) => state.product.loyaltyPoints);
  const discountPercentage = useAppSelector(
    (state) => state.product.discountPercentage
  );

  // Format prices for display
  const formattedCurrentPrice = useMemo(() => {
    return `₹${currentPrice.toLocaleString()}`;
  }, [currentPrice]);

  const formattedOriginalPrice = useMemo(() => {
    return `₹${originalPrice.toLocaleString()}`;
  }, [originalPrice]);

  // Check if there's a discount
  const hasDiscount = useMemo(() => {
    return originalPrice > currentPrice && discountPercentage > 0;
  }, [originalPrice, currentPrice, discountPercentage]);

  // Default cashback percentage (can be made configurable)
  const cashbackPercentage = 2;

  return useMemo(
    (): UseProductPricingReturn => ({
      currentPrice,
      originalPrice,
      discountPercentage,
      loyaltyPoints,
      cashbackPercentage,
      formattedCurrentPrice,
      formattedOriginalPrice,
      hasDiscount,
    }),
    [
      currentPrice,
      originalPrice,
      discountPercentage,
      loyaltyPoints,
      cashbackPercentage,
      formattedCurrentPrice,
      formattedOriginalPrice,
      hasDiscount,
    ]
  );
};

/**
 * Hook for calculating total price based on quantity
 */
export const useTotalPrice = () => {
  const currentPrice = useAppSelector((state) => state.product.currentPrice);
  const quantity = useAppSelector((state) => state.cart.quantity);

  return useMemo(() => {
    const total = currentPrice * quantity;
    return {
      total,
      formattedTotal: `₹${total.toLocaleString()}`,
      savings: 0, // Can be calculated based on discounts
    };
  }, [currentPrice, quantity]);
};

/**
 * Hook for accessing loyalty and cashback information
 */
export const useLoyaltyInfo = () => {
  const loyaltyPoints = useAppSelector((state) => state.product.loyaltyPoints);
  const currentPrice = useAppSelector((state) => state.product.currentPrice);
  const quantity = useAppSelector((state) => state.cart.quantity);
  const cashbackPercentage = 2; // Default cashback percentage

  return useMemo(() => {
    const totalLoyaltyPoints = loyaltyPoints * quantity;
    const cashbackAmount = (currentPrice * quantity * cashbackPercentage) / 100;

    return {
      loyaltyPoints,
      totalLoyaltyPoints,
      cashbackPercentage,
      cashbackAmount,
      formattedCashbackAmount: `₹${cashbackAmount.toFixed(2)}`,
    };
  }, [loyaltyPoints, currentPrice, quantity, cashbackPercentage]);
};

/**
 * Hook for price comparison and savings calculation
 */
export const usePriceComparison = () => {
  const currentPrice = useAppSelector((state) => state.product.currentPrice);
  const originalPrice = useAppSelector((state) => state.product.originalPrice);
  const quantity = useAppSelector((state) => state.cart.quantity);

  return useMemo(() => {
    const totalCurrentPrice = currentPrice * quantity;
    const totalOriginalPrice = originalPrice * quantity;
    const totalSavings = totalOriginalPrice - totalCurrentPrice;
    const savingsPercentage =
      originalPrice > 0 ? (totalSavings / totalOriginalPrice) * 100 : 0;

    return {
      totalCurrentPrice,
      totalOriginalPrice,
      totalSavings,
      savingsPercentage,
      formattedTotalSavings: `₹${totalSavings.toLocaleString()}`,
      hasSavings: totalSavings > 0,
    };
  }, [currentPrice, originalPrice, quantity]);
};

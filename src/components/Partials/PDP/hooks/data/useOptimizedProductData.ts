import { useMemo } from "react";
import { useAppSelector } from "@/store/hooks";
import { UseProductDataReturn } from "../../types";

/**
 * Optimized hook for accessing product data with no loading states
 * Assumes data is already available from server-side rendering
 */
export const useOptimizedProductData = (): UseProductDataReturn => {
  const strapiProduct = useAppSelector((state) => state.product.strapiProduct);
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);

  // No loading states - data should be immediately available
  const result = useMemo(
    (): UseProductDataReturn => ({
      strapiProduct,
      medusaProduct,
      isLoading: false, // Always false for optimized loading
      isError: false,   // Handle errors at page level
      error: null,
    }),
    [strapiProduct, medusaProduct]
  );

  return result;
};

/**
 * Hook for accessing only Strapi product data (optimized)
 */
export const useOptimizedStrapiProduct = () => {
  return useAppSelector((state) => state.product.strapiProduct);
};

/**
 * Hook for accessing only Medusa product data (optimized)
 */
export const useOptimizedMedusaProduct = () => {
  return useAppSelector((state) => state.product.medusaProduct);
};

/**
 * Hook for checking if product data is available (optimized)
 */
export const useOptimizedProductAvailability = () => {
  const strapiProduct = useAppSelector((state) => state.product.strapiProduct);
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);

  return useMemo(
    () => ({
      hasData: !!(strapiProduct || medusaProduct),
      hasStrapiData: !!strapiProduct,
      hasMedusaData: !!medusaProduct,
      hasCompleteData: !!(strapiProduct && medusaProduct),
      isReady: true, // Always ready with optimized loading
    }),
    [strapiProduct, medusaProduct]
  );
};

/**
 * Hook for accessing product metadata (optimized)
 */
export const useOptimizedProductMetadata = () => {
  const strapiProduct = useAppSelector((state) => state.product.strapiProduct);
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);

  return useMemo(
    () => ({
      title: medusaProduct?.title || strapiProduct?.title || "Product",
      handle: medusaProduct?.handle || "",
      description:
        medusaProduct?.description || strapiProduct?.short_description || "",
      shortDescription: strapiProduct?.short_description || "",
      additionalDescription: strapiProduct?.additional_description || "",
      tags: medusaProduct?.tags || [],
      categories: medusaProduct?.categories || [],
      collection: medusaProduct?.collection || null,
    }),
    [strapiProduct, medusaProduct]
  );
};

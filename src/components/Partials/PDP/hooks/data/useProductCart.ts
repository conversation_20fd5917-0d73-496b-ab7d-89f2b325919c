import { useMemo, useState, useCallback } from "react";
import { useProductCart as useReduxProductCart } from "@/store/hooks";
import { useAppSelector } from "@/store/hooks";
import { UseProductCartReturn } from "../../types";

/**
 * Hook for managing cart operations and quantity
 * Provides cart-related functionality for the product
 */
export const useProductCart = (): UseProductCartReturn => {
  const {
    quantity,
    setQuantity,
    incrementQuantity,
    decrementQuantity,
    addToCart,
  } = useReduxProductCart();

  const activeVariant = useAppSelector((state) => state.product.activeVariant);
  const medusaProduct = useAppSelector((state) => state.product.medusaProduct);
  const isLoading = useAppSelector((state) => state.product.isLoading);

  const [isAddingToCart, setIsAddingToCart] = useState(false);

  // Check if product can be added to cart
  const canAddToCart = useMemo(() => {
    const inventoryQuantity = activeVariant?.inventory_quantity ?? 0;
    return !!(
      medusaProduct &&
      activeVariant &&
      inventoryQuantity > 0 &&
      quantity > 0 &&
      quantity <= inventoryQuantity &&
      !isLoading &&
      !isAddingToCart
    );
  }, [medusaProduct, activeVariant, quantity, isLoading, isAddingToCart]);

  // Wrapper for add to cart with loading state
  const handleAddToCart = useCallback(async () => {
    if (!canAddToCart) return;

    setIsAddingToCart(true);
    try {
      await addToCart();
    } finally {
      setIsAddingToCart(false);
    }
  }, [canAddToCart, addToCart]);

  return useMemo(
    (): UseProductCartReturn => ({
      quantity,
      setQuantity,
      incrementQuantity,
      decrementQuantity,
      addToCart: handleAddToCart,
      isAddingToCart,
      canAddToCart,
    }),
    [
      quantity,
      setQuantity,
      incrementQuantity,
      decrementQuantity,
      handleAddToCart,
      isAddingToCart,
      canAddToCart,
    ]
  );
};

/**
 * Hook for quantity validation and constraints
 */
export const useQuantityConstraints = () => {
  const activeVariant = useAppSelector((state) => state.product.activeVariant);
  const quantity = useAppSelector((state) => state.cart.quantity);

  return useMemo(() => {
    const maxQuantity = activeVariant?.inventory_quantity ?? 0;
    const minQuantity = 1;

    return {
      minQuantity,
      maxQuantity,
      isAtMin: quantity <= minQuantity,
      isAtMax: quantity >= maxQuantity,
      isValidQuantity: quantity >= minQuantity && quantity <= maxQuantity,
      availableStock: maxQuantity,
      hasStock: maxQuantity > 0,
    };
  }, [activeVariant?.inventory_quantity, quantity]);
};

/**
 * Hook for cart button states and messages
 */
export const useCartButtonState = () => {
  const { canAddToCart, isAddingToCart } = useProductCart();
  const activeVariant = useAppSelector((state) => state.product.activeVariant);
  const { hasStock, isValidQuantity } = useQuantityConstraints();

  return useMemo(() => {
    let buttonText = "Add to Cart";
    let buttonDisabled = false;
    let buttonVariant: "default" | "destructive" | "outline" | "secondary" =
      "default";

    if (isAddingToCart) {
      buttonText = "Adding...";
      buttonDisabled = true;
    } else if (!activeVariant) {
      buttonText = "Select Variant";
      buttonDisabled = true;
      buttonVariant = "outline";
    } else if (!hasStock) {
      buttonText = "Out of Stock";
      buttonDisabled = true;
      buttonVariant = "destructive";
    } else if (!isValidQuantity) {
      buttonText = "Invalid Quantity";
      buttonDisabled = true;
      buttonVariant = "destructive";
    } else if (!canAddToCart) {
      buttonText = "Cannot Add to Cart";
      buttonDisabled = true;
      buttonVariant = "outline";
    }

    return {
      buttonText,
      buttonDisabled,
      buttonVariant,
      canAddToCart,
      isAddingToCart,
    };
  }, [canAddToCart, isAddingToCart, activeVariant, hasStock, isValidQuantity]);
};

/**
 * Hook for cart-related notifications and messages
 */
export const useCartMessages = () => {
  const activeVariant = useAppSelector((state) => state.product.activeVariant);
  const { availableStock, hasStock } = useQuantityConstraints();

  return useMemo(() => {
    const messages: string[] = [];

    if (!hasStock) {
      messages.push("This item is currently out of stock");
    } else if (availableStock <= 5) {
      messages.push(`Only ${availableStock} items left in stock`);
    }

    if (activeVariant?.inventory_quantity && availableStock <= 10) {
      messages.push("Order soon - limited stock available");
    }

    return {
      messages,
      hasMessages: messages.length > 0,
      stockWarning: availableStock <= 5 && hasStock,
      urgencyMessage: availableStock <= 10 && hasStock,
    };
  }, [activeVariant, availableStock, hasStock]);
};

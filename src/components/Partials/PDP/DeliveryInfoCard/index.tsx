"use client";
import Location from "@/assets/icons/Location";
import React, { useState } from "react";
import { useProductTheme } from "../hooks/ui";

/**
 * DeliveryInfo Component
 *
 * Displays delivery information with pincode checking functionality.
 * Uses ProductThemeContext for consistent theming.
 */
const DeliveryInfo: React.FC = () => {
  const { primaryColor } = useProductTheme();
  const [hasPincode, setPincode] = useState(false);
  const [pincodeInput, setPincodeInput] = useState("");
  const [error, setError] = useState("");

  // List of valid pincodes (you can expand this or connect to an API)
  const validPincodes = ["394190", "110001", "400001", "560001", "600001"];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ""); // Only allow digits
    if (value.length <= 6) {
      setPincodeInput(value);
      setError(""); // Clear error when user types
    }
  };

  const handleCheckPincode = () => {
    if (pincodeInput.length === 6) {
      if (validPincodes.includes(pincodeInput)) {
        setPincode(true);
        setError("");
      } else {
        setError("Sorry, delivery is not available for this pincode.");
      }
    }
  };

  const handleReset = () => {
    setPincode(false);
    setPincodeInput("");
    setError("");
  };

  return (
    <div className="pt-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-0.5">
          <Location color={primaryColor} />
          {hasPincode ? (
            <p className="text-[#1a181e] text-sm leading-5 font-medium font-obviously">
              Delivery Options for
              <span className="font-semibold"> {pincodeInput}</span>
            </p>
          ) : (
            <p className="text-[#1a181e] text-sm leading-5 font-medium font-obviously">
              Delivery Options
            </p>
          )}
        </div>

        {hasPincode ? (
          <button
            onClick={handleReset}
            className="ml-2 underline text-black text-sm font-obviously cursor-pointer font-medium"
            style={{ color: primaryColor }}
          >
            Change
          </button>
        ) : null}
      </div>

      {!hasPincode ? (
        <div className="pl-5 mt-4">
          <div className="flex items-center justify-between gap-3">
            <input
              type="text"
              inputMode="numeric"
              name="pin"
              value={pincodeInput}
              onChange={handleInputChange}
              maxLength={6}
              placeholder="Enter pincode"
              className="border rounded-[6px] py-2.5 px-4 text-sm font-medium leading-5 w-full font-obviously flex-1 focus:outline-none focus:ring-0"
              style={{ borderColor: primaryColor }}
            />
            <button
              onClick={handleCheckPincode}
              disabled={pincodeInput.length !== 6}
              className={`w-30 rounded-[6px] text-white text-center p-2 text-base font-semibold leading-6 ${
                pincodeInput.length === 6
                  ? "cursor-pointer"
                  : "disabled:opacity-50"
              }`}
              style={{ backgroundColor: primaryColor }}
            >
              Check
            </button>
          </div>

          {error && (
            <p className="text-red-500 text-sm font-medium mt-2 font-obviously">
              {error}
            </p>
          )}
        </div>
      ) : null}

      {hasPincode ? (
        <div className="py-3">
          <p className="text-xs font-obviously leading-4">
            Yay! Your pincode is eligible for delivery.
          </p>
          <p
            className="text-sm font-medium leading-5 text-black font-obviously"
            style={{ color: primaryColor }}
          >
            Delivery by <span className="font-semibold">Thursday, 5 June</span>
          </p>
        </div>
      ) : null}
    </div>
  );
};

export default DeliveryInfo;

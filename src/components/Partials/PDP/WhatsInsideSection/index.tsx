"use client";

import React from "react";
import Image from "next/image";
import { getStrapiUrl } from "@/utils/strapiUrl";
import { UnderLine } from "@/assets/icons/UnderLine";
import { WhatsInsideType } from "@/types/PDP/WhatInside";

interface WhatsInsideSectionProps {
  data?: WhatsInsideType;
}

const WhatsInsideSection: React.FC<WhatsInsideSectionProps> = ({ data }) => {
  // Early return if no data or component should not be shown
  if (!data || !data.show_component) {
    return null;
  }

  return (
    <div className="mt-4 flex flex-col justify-center w-full">
      <h1 className="font-narrow font-semibold mb-4 text-4xl leading-8 text-[#1a181e]">
        {data.title}
      </h1>

      {data.whats_inside_details.map((detail, index) => (
        <React.Fragment key={index}>
          <div className="relative w-full h-[325px] flex items-center justify-center">
            <div className="relative w-[325px] h-[325px]">
              <Image
                src={getStrapiUrl(detail.image?.web?.url) || ""}
                alt={detail.image?.web?.alternativeText || ""}
                fill
                style={{ objectFit: "contain" }}
                className="object-contain"
              />
            </div>
          </div>
          <div>
            <h2 className="font-narrow font-semibold mb-6 text-2xl leading-8 text-[#1a181e]">
              {detail.title}
            </h2>
            <ul className="list-none">
              {detail.details.map((item, itemIndex) => (
                <li
                  key={itemIndex}
                  className="text-lg leading-7 font-normal text-[#1a181e] font-obviously flex items-start"
                >
                  <div className="flex-shrink-0">{item.key}</div>
                  <div className="flex-1 border-t-2 border-dotted border-[#00000080] mx-2 mt-[18px]"></div>
                  <div className="flex-shrink-0">{item.value}%</div>
                </li>
              ))}
              <li className="text-black pt-2.5 text-2xl font-normal leading-8 font-gooddog flex items-end justify-end flex-col">
                <UnderLine />
                100%
              </li>
            </ul>
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};

export default WhatsInsideSection;

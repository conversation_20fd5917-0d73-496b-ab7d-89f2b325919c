"use client";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import React, { useState } from "react";
import Image from "next/image";
import { getStrapiUrl } from "@/utils/strapiUrl";
import { cn } from "@/libs/utils";
import FeatureHighlightSection from "@/components/blocks/FeatureHighlightSection";

const WhySection = ({ productData }: { productData: ProductDetailsType }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const title = productData.why_section?.title;
  const description = productData.why_section?.description;
  const primaryColor = productData.primary_color;

  console.log(description, "i am description");

  const imageUrl = getStrapiUrl(
    productData.why_section?.primary_image?.web?.url
  );

  const toggleReadMore = () => {
    setIsExpanded((prev) => !prev);
  };

  return (
    <div className="max-w-[848px] mx-auto">
      <div className="px-6 py-5 flex flex-col md:flex-row items-start gap-6 w-full">
        <div
          className={cn(`flex-1 max-w-[540px]`, {
            "self-center": !isExpanded,
          })}
        >
          <h4 className="mb-3 font-narrrow font-[560] text-[28px] text-black">
            {title}
          </h4>
          <div className=" mb-3 text-sm font-normal font-obviously text-[#1a181e]">
            <p
              className={`${
                isExpanded
                  ? "line-clamp-none"
                  : "line-clamp-1 overflow-hidden text-ellipsis"
              } transition-all duration-300 ease-in-out`}
            >
              {description}
            </p>
            {description && (
              <button
                onClick={toggleReadMore}
                className="font-[560] underline font-obviously cursor-pointer text-[#1a181e] hover:text-black transition-colors duration-200 mt-1 inline-block"
                aria-expanded={isExpanded}
                aria-label={isExpanded ? "Show less text" : "Show more text"}
              >
                {isExpanded ? "READ LESS" : "READ MORE"}
              </button>
            )}
          </div>
        </div>

        {imageUrl && (
          <div className="flex-1 md:max-w-[315px] w-full shrink-0">
            <Image
              src={imageUrl}
              alt="Why Section"
              width={315}
              height={300}
              className="w-full h-auto md:w-[315px] md:h-[300px]"
              sizes="(max-width: 768px) 100vw, 315px"
            />
          </div>
        )}
      </div>
      <FeatureHighlightSection
        backgroundColor="transparent"
        className="md:px-0 md:py-0 mt-[-32px]"
        images={
          productData.why_section?.image_carousels?.images.map((image) =>
            getStrapiUrl(image.web?.url)
          ) || []
        }
        controls={true}
        showScrollbar={true}
        scrollbarThumbColor={primaryColor}
      />
    </div>
  );
};

export default WhySection;

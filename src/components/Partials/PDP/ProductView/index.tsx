"use client";

import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import React from "react";
import { ProductDetailsPage } from "../ProductDetailsPage";
import { ExtendedMedusaProductWithStrapiProduct } from "@/types/Medusa/Product";

/**
 * ProductView Component
 *
 * Simplified wrapper component that renders the ProductDetailsPage
 * Data is already hydrated in Redux store via HydratedProductPage
 */

const ProductView = ({
  productDataOk,
  medusaProductData,
}: {
  medusaProductData?: ExtendedMedusaProductWithStrapiProduct;
  productDataOk?: ProductDetailsType;
} = {}) => {
  console.log("productDataOk", productDataOk);
  console.log("medusaProductData", medusaProductData);

  const handleAddToCart = (cartData: any) => {
    console.log("Adding to cart:", cartData);
    // Handle add to cart logic here
  };

  const handleCouponClick = () => {
    console.log("Coupon clicked");
    // Handle coupon click logic here
  };

  // Data is already hydrated in Redux store, so we can render immediately
  return (
    <ProductDetailsPage
      onAddToCart={handleAddToCart}
      onCouponClick={handleCouponClick}
    />
  );
};

export default ProductView;

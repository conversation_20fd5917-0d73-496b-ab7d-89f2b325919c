"use client";

import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
} from "@/components/ui/accordion";
import { CustomAccordionTrigger } from "@/components/Common/FAQAccordion/CustomAccordionTrigger";
import { cn } from "@/libs/utils";
import { ProductDetailsExtraType } from "@/types/PDP/ProductDetailsExtra";
import { useProductTheme } from "../hooks/ui";

/**
 * ProductDetailsAccordion Component
 *
 * Displays product details in an expandable accordion format.
 * Uses ProductThemeContext for consistent theming.
 *
 * @param data - Product details data
 * @param borderColor - Optional border color override
 * @param iconColor - Optional icon color override
 * @param className - Additional CSS classes
 */
export interface ProductDetailsAccordionProps {
  data?: ProductDetailsExtraType;
  borderColor?: string;
  iconColor?: string;
  className?: string;
}

export function ProductDetailsAccordion({
  data,
  borderColor,
  className,
}: ProductDetailsAccordionProps) {
  const { primaryColor } = useProductTheme();

  // Use provided colors or fall back to theme color
  const finalBorderColor = borderColor || primaryColor;
  const chevronColor = "black";

  // Early return if no data or component should not be shown
  if (!data || !data.show_component) {
    return null;
  }

  return (
    <Accordion
      type="single"
      collapsible
      className={cn(`border rounded-md overflow-hidden mt-6`, className)}
      style={{ borderColor: finalBorderColor }}
    >
      <AccordionItem
        className="bg-transparent border-0 border-b last:border-b-0"
        value="product-details"
        style={{ borderBottomColor: finalBorderColor }}
      >
        <CustomAccordionTrigger
          className="px-6 py-4 text-left text-lg lg:text-[22px] font-narrow font-semibold hover:no-underline cursor-pointer"
          iconColor={chevronColor}
        >
          Product Details
        </CustomAccordionTrigger>
        <AccordionContent className="px-6 font-normal text-base lg:text-lg font-obviously">
          {/* Main nutritional items */}
          <ul className="list-none">
            {data.product_detail_extra_items?.map((detail, detailIndex) => (
              <li key={detailIndex}>
                {/* Main item */}
                <div className="text-base leading-7 font-normal text-[#1a181e] font-obviously flex items-start mb-2">
                  <strong className="flex-shrink-0">{detail.key}: </strong>
                  <div className="ml-1.5">{detail.value}</div>
                </div>
              </li>
            ))}
          </ul>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}

export default ProductDetailsAccordion;

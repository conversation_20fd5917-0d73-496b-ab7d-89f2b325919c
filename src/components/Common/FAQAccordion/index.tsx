"use client";

import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
} from "@/components/ui/accordion";
import { CustomAccordionTrigger } from "./CustomAccordionTrigger";
import { cn } from "@/libs/utils";

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

export interface FAQAccordionProps {
  items: FAQItem[];
  borderColor?: string;
  iconColor?: string;
  className?: string;
}

export function FAQAccordion({
  items,
  borderColor = "#be259a",
  iconColor,
  className,
}: FAQAccordionProps) {
  // Use borderColor for iconColor if not specified
  const chevronColor = iconColor || borderColor;

  return (
    <Accordion
      type="single"
      collapsible
      className={cn(`border rounded-md overflow-hidden`, className)}
      style={{ borderColor }}
    >
      {items.map((item) => (
        <AccordionItem
          key={item.id}
          value={item.id}
          className="bg-transparent border-0 border-b last:border-b-0"
          style={{ borderBottomColor: borderColor }}
        >
          <CustomAccordionTrigger
            className="p-4 text-left text-lg lg:text-2xl font-narrow font-semibold hover:no-underline"
            iconColor={chevronColor}
          >
            {item.question}
          </CustomAccordionTrigger>
          <AccordionContent className="px-4 font-normal text-base lg:text-lg font-obviously">
            {item.answer}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}

export default FAQAccordion;

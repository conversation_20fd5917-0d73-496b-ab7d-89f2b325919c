// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface Image {
  web?: { data: Media };
  mobile?: { data: Media };
}
export interface Image_Plain {
  web?: Media_Plain;
  mobile?: Media_Plain;
}

export interface Image_NoRelations {
  web?: number;
  mobile?: number;
}

